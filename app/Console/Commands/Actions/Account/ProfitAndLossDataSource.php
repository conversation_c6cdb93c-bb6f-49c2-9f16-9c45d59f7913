<?php

namespace App\Console\Commands\Actions\Account;

use App\Console\Commands\Action;

class ProfitAndLossDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:profitAndLoss {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $financial_year = $this->input['financial_year'] ?? date('Y').'-'.(date('Y')+1);
        $years = explode('-',$financial_year);
        $type = $this->input['periodType'] ?? 'yearly';
        $month = $this->input['financial_month'] ?? 3;
        $asondateval = $this->input['as_on_date'] ?? date('Y-m-d');
        $soc_id = $this->input['company_id'];
        $inputValidation = $this->validateInput(array(
            "financial_year"=>$financial_year,
            "type"=>$type,
            "month"=>$month,
            "asondateval"=>$asondateval,
            "soc_id"=>$soc_id
        ));

        if ($inputValidation["status"] == "error") {
            $this->message = $inputValidation["message"];
            $this->status = $inputValidation["status"];
            $this->statusCode = 400;
            return;
        }

        $prev_financial_year = ($years[0]-1).'_'.$years[0];
        $financial_year = $years[0].'_'.$years[1];

        $fy_start_date = $years[0].'-04-01';
        $fy_end_date = $years[1].'-03-31';
        $fy_end_month = "";
        $fy_start_date_prev = ($years[0]-1).'-04-01';
        $fy_end_date_prev = $years[0].'-03-31';

        if($type == "asonmonth" || $type == "monthly"){
            if($month > 3){
                $fy_end_date = $years[0].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[0].'-'.sprintf("%02d", $month).'-01'));
            }else{
                $fy_end_date = $years[1].'-'.sprintf("%02d", $month).'-'.date('t', strtotime($years[1].'-'.sprintf("%02d", $month).'-01'));
            }
        }else if($type == "asondate"){
            $fy_end_date = $asondateval;
        }
        if($type == "monthly"){
            if($month > 3){
                $fy_start_date = $years[0].'-'.sprintf("%02d", $month).'-01';
                $fy_end_month = $years[0].'-'.sprintf("%02d", $month);
                $fy_end_month_prev = ($years[0]-1).'-'.sprintf("%02d", $month);
            }else{
                $fy_start_date = $years[1].'-'.sprintf("%02d", $month).'-01';
                $fy_end_month = $years[1].'-0'.$month;
                $fy_end_month_prev = ($years[1]-1).'-0'.$month;
            }
        }

        $conditionCurrent = 'WHERE TR.transaction_date BETWEEN "'.$fy_start_date.'" AND "'.$fy_end_date.'"';
        $conditionPrevious = 'WHERE TRP.transaction_date BETWEEN "'.$fy_start_date_prev.'" AND "'.$fy_end_date_prev.'"';

        // dd($conditionPrevious, $conditionCurrent);
        $query = "WITH RECURSIVE LedgerTree AS (
            SELECT ledger_account_id, ledger_account_name, nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context, report_head
            FROM chsone_grp_ledger_tree
            WHERE parent_id = 0
      
            UNION ALL
      
            SELECT child.ledger_account_id, child.ledger_account_name, child.nature_of_account, child.entity_type, child.behaviour, child.parent_id, parent.level + 1, child.status, child.context, child.report_head
            FROM chsone_grp_ledger_tree AS child
            INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
        )
        SELECT *
        FROM LedgerTree
        WHERE report_head LIKE '%profit and loss%' OR report_head LIKE '%Profit & Loss%'
        ORDER BY level, ledger_account_id;";

        $query = $this->filter($query);

        $ledgerData = $this->tenantDB()->select($query);

        // $transactionData = $this->tenantDB()->table('chsone_grp_ledger_tree as LR')
        // ->select(
        //     'LR.ledger_account_id',
        //     'LR.ledger_account_name',
        //     'LR.nature_of_account',
        //     'LR.entity_type',
        //     'LR.behaviour',
        //     'LR.parent_id'
        // )
        // ->selectRaw('SUM(IF(TR.is_opening_balance =  1, IF(TR.transaction_type = "dr", TR.transaction_amount, (-1 * TR.transaction_amount)), 0)) AS opening_balance')
        // ->selectRaw('SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) - SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) as transaction_amount')
        // ->leftJoin('chsone_ledger_transactions as TR', 'LR.ledger_account_id', '=', 'TR.ledger_account_id')
        // ->where('TR.ledger_account_id', 108)
        // ->whereBetween('TR.transaction_date', [$fy_start_date, $fy_end_date])
        // ->groupBy('LR.ledger_account_id')
        // ->get();

        // $query_string = " (SELECT *,CASE WHEN ledger_account_id IN ($condition) THEN @idlist := CONCAT(ledger_account_id) "
        // . "WHEN FIND_IN_SET(parent_id,@idlist) THEN @idlist := CONCAT(@idlist,',',ledger_account_id) END as checkId "
        // . "FROM chsone_grp_ledger_tree ORDER BY parent_id, ledger_account_name, ledger_account_id ASC) as T ";

        // $transactionData = json_decode($transactionData, true);

        // $transactionQuery = 'SELECT  
        // LR.ledger_account_id, LR.ledger_account_name, LR.nature_of_account, LR.entity_type, LR.behaviour, LR.parent_id,
        // SUM(IF(TR.is_opening_balance =  1, IF(TR.transaction_type = "dr", TR.transaction_amount, (-1 * TR.transaction_amount)), 0)) AS opening_balance,
        // SUM(IF(TR.transaction_type = "dr", TR.transaction_amount, 0)) - SUM(IF(TR.transaction_type = "cr", TR.transaction_amount, 0)) as transaction_amount
        // FROM chsone_grp_ledger_tree AS LR
        // LEFT  JOIN chsone_ledger_transactions AS TR on LR.ledger_account_id = TR.ledger_account_id
        // WHERE 
        //     TR.transaction_date BETWEEN "2020-04-01" AND "2021-03-31"
        // GROUP BY LR.ledger_account_id;';

        $transactionQuery = "WITH LedgerSummaryCurrent AS (
            SELECT  
                LR.ledger_account_id, 
                LR.ledger_account_name, 
                LR.nature_of_account, 
                LR.entity_type, 
                LR.behaviour, 
                LR.parent_id,
                SUM(IF(TR.is_opening_balance = 1, IF(TR.transaction_type = 'dr', TR.transaction_amount, -TR.transaction_amount), 0)) AS prev_financial_year_balance,
                SUM(IF(TR.transaction_type = 'dr', TR.transaction_amount, 0)) AS sum_dr,
                SUM(IF(TR.transaction_type = 'cr', TR.transaction_amount, 0)) AS sum_cr
            FROM chsone_grp_ledger_tree AS LR
            LEFT JOIN chsone_ledger_transactions AS TR ON LR.ledger_account_id = TR.ledger_account_id
            $conditionCurrent
            GROUP BY LR.ledger_account_id
        ),
        LedgerSummaryPrevious AS (
            SELECT  
                LRP.ledger_account_id AS previous_ledger_account_id, 
                LRP.ledger_account_name AS previous_ledger_account_name, 
                LRP.nature_of_account, 
                LRP.entity_type, 
                LRP.behaviour, 
                LRP.parent_id,
                SUM(IF(TRP.is_opening_balance = 1, IF(TRP.transaction_type = 'dr', TRP.transaction_amount, -TRP.transaction_amount), 0)) AS p_prev_financial_year_balance,
                SUM(IF(TRP.transaction_type = 'dr', TRP.transaction_amount, 0)) AS p_sum_dr,
                SUM(IF(TRP.transaction_type = 'cr', TRP.transaction_amount, 0)) AS p_sum_cr
            FROM chsone_grp_ledger_tree AS LRP
            LEFT JOIN chsone_ledger_transactions AS TRP ON LRP.ledger_account_id = TRP.ledger_account_id
            $conditionPrevious
            GROUP BY LRP.ledger_account_id
        ),
        MonthlySummary AS (
            SELECT  
                TR.ledger_account_id,
                SUM(IF(TR.transaction_type = 'dr', TR.transaction_amount, 0)) AS transaction_total_debit,
                SUM(IF(TR.transaction_type = 'cr', TR.transaction_amount, 0)) AS transaction_total_credit
            FROM chsone_ledger_transactions AS TR
            WHERE TR.transaction_date LIKE '$fy_end_month%'
            GROUP BY TR.ledger_account_id
        )

        SELECT  
            LCS.ledger_account_id, 
            LSP.previous_ledger_account_id AS previous_id,
            LCS.ledger_account_name AS current_ledger_account_name,
            LSP.previous_ledger_account_name,
            LCS.nature_of_account, 
            LCS.entity_type, 
            LCS.behaviour, 
            LCS.parent_id, 
            LCS.sum_dr, 
            LCS.sum_cr, 
            MS.transaction_total_debit, 
            MS.transaction_total_credit, 
            LSP.p_prev_financial_year_balance, 
            LSP.p_sum_dr, 
            LSP.p_sum_cr,
            CASE 
                WHEN LCS.nature_of_account = 'dr' THEN 
                    CASE 
                        WHEN LSP.p_sum_cr = 0 THEN LSP.p_prev_financial_year_balance + LSP.p_sum_dr
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr > 0 AND LSP.p_sum_cr > 0) THEN LSP.p_sum_dr - LSP.p_sum_cr 
                        ELSE -LSP.p_prev_financial_year_balance
                    END
                ELSE
                    CASE 
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr = 0 AND LCS.nature_of_account = 'cr') THEN LSP.p_sum_cr
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr > 0 AND LSP.p_sum_cr > 0) THEN LSP.p_sum_cr - LSP.p_sum_dr
                        ELSE LSP.p_prev_financial_year_balance
                    END
            END AS $prev_financial_year,
            CASE 
                WHEN (LCS.nature_of_account = 'dr' AND '$type' != 'monthly') THEN 
                    CASE 
                        WHEN LCS.sum_dr > LCS.sum_cr THEN LCS.sum_dr - LCS.sum_cr
                        ELSE -1 * (LCS.sum_cr - LCS.sum_dr)
                    END
                WHEN (LCS.nature_of_account = 'dr' AND '$type' = 'monthly') THEN 
                    CASE
                        WHEN MS.transaction_total_debit IS NOT NULL OR MS.transaction_total_credit IS NOT NULL THEN LCS.prev_financial_year_balance + COALESCE(MS.transaction_total_debit, 0) - COALESCE(MS.transaction_total_credit, 0)
                        ELSE LCS.prev_financial_year_balance
                    END
                WHEN LCS.nature_of_account = 'cr' THEN
                    CASE 
                        WHEN ('$type' = 'monthly') THEN -1 * (LCS.prev_financial_year_balance + COALESCE(MS.transaction_total_debit, 0) - COALESCE(MS.transaction_total_credit, 0))
                        ELSE LCS.sum_cr - LCS.sum_dr
                    END
                ELSE 
                    LCS.sum_cr - LCS.sum_dr
            END AS $financial_year
        FROM LedgerSummaryCurrent LCS
        LEFT JOIN LedgerSummaryPrevious LSP ON LCS.ledger_account_id = LSP.previous_ledger_account_id
        LEFT JOIN MonthlySummary MS ON LCS.ledger_account_id = MS.ledger_account_id
        UNION
        SELECT  
            LSP.previous_ledger_account_id AS ledger_account_id, 
            LSP.previous_ledger_account_id AS previous_id,
            NULL AS current_ledger_account_name,
            LSP.previous_ledger_account_name,
            LSP.nature_of_account, 
            LSP.entity_type, 
            LSP.behaviour, 
            LSP.parent_id, 
            NULL AS sum_dr, 
            NULL AS sum_cr, 
            MS.transaction_total_debit, 
            MS.transaction_total_credit, 
            LSP.p_prev_financial_year_balance, 
            LSP.p_sum_dr, 
            LSP.p_sum_cr,
            CASE 
                WHEN LSP.nature_of_account = 'dr' THEN 
                    CASE 
                        WHEN LSP.p_sum_cr = 0 THEN LSP.p_prev_financial_year_balance + LSP.p_sum_dr
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr > 0 AND LSP.p_sum_cr > 0) THEN LSP.p_sum_dr - LSP.p_sum_cr 
                        ELSE -LSP.p_prev_financial_year_balance
                    END
                ELSE
                    CASE 
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr = 0 AND LSP.nature_of_account = 'cr') THEN LSP.p_sum_cr
                        WHEN (LSP.p_prev_financial_year_balance = 0 AND LSP.p_sum_dr > 0 AND LSP.p_sum_cr > 0) THEN LSP.p_sum_cr - LSP.p_sum_dr
                        ELSE LSP.p_prev_financial_year_balance
                    END
            END AS $prev_financial_year,
            NULL AS $financial_year
        FROM LedgerSummaryPrevious LSP
        LEFT JOIN MonthlySummary MS ON LSP.previous_ledger_account_id = MS.ledger_account_id";

        $transactionData = $this->tenantDB()->select($transactionQuery);
        // dd($transactionData);
        // print_r($transactionData);
        // exit;

        $transactionFormat = [
            'ledger_account_id' => [
                'ledger_account_id' => 'casting:int,0',
                'ledger_account_name' => '',
                'nature_of_account' => '',
                'entity_type' => '',
                'behaviour' => '',
                'context' => '',
                'status' => '',
                $financial_year => 'casting:double,0.0',
                $prev_financial_year => 'casting:double,0.0'
            ]

        ];

        $transactionData = $this->smartFormat($transactionData, $transactionFormat);

        // build the tree stucture
        $tree = $this->buildTree($ledgerData, $transactionData, 0, $financial_year, $prev_financial_year);

        // Filter data to get only income
        $income = collect($tree)->firstWhere('ledger_account_name', 'income');
        $expense = collect($tree)->firstWhere('ledger_account_name', 'expense');

        // Calculate the differences
        $financialProfit = 0;
        $financialLoss = 0;
        $previousProfit = 0;
        $previousLoss = 0;


        if($income[$prev_financial_year] > $expense[$prev_financial_year]) {
            $previousProfit = $income[$prev_financial_year] - $expense[$prev_financial_year];
        }
        if($expense[$prev_financial_year] > $income[$prev_financial_year]) {
            $previousLoss = $expense[$prev_financial_year] - $income[$prev_financial_year];
        }
        if($expense[$financial_year] > $income[$financial_year]) {
            $financialLoss = $expense[$financial_year] - $income[$financial_year];
        }
        if($income[$financial_year] > $expense[$financial_year]) {
            $financialProfit = $income[$financial_year] - $expense[$financial_year];
        }

        $lastId = end($tree)['id'];
        
        if($financialProfit > 0 || $previousProfit > 0) {
            $profit = [
                'id'                    => ++$lastId,
                'ledger_account_name'   => 'To Net Profit',
                'nature_of_account'     => 'cr',
                'context'               => 'profit',
                $prev_financial_year    => round($previousProfit, 2),
                $financial_year         => round($financialProfit, 2)
            ];
            $tree[] = $profit;
        }

        if($financialLoss > 0 || $previousLoss > 0) {
            $loss = [
                'id'                    => ++$lastId,
                'ledger_account_name'   => 'By Net Loss',
                'nature_of_account'     => 'dr',
                'context'               => 'loss',
                $prev_financial_year    => round($previousLoss, 2),
                $financial_year         => round($financialLoss, 2)
            ];
            $tree[] = $loss;
        }

        // $total = [
        //     'id'                    => ++$lastId,
        //     'ledger_account_name'   => 'Total',
        //     'context'               => 'total',
        //     $prev_financial_year    => ($income[$prev_financial_year] > $expense[$prev_financial_year]) ? $income[$prev_financial_year] : $expense[$prev_financial_year],
        //     $financial_year         => ($income[$financial_year] > $expense[$financial_year]) ? $income[$financial_year] : $expense[$financial_year]
        // ];

        // $tree[] = $total;

        $table = [];

        $firstRow = [$expense];
        if ($financialProfit > 0 || $previousProfit > 0) {
            $firstRow[] = $profit;
        }
        // $firstRow[] = $total;
        $table[] = $firstRow;

        $secondRow = [$income];
        if ($financialLoss > 0 || $previousLoss > 0) {
            $secondRow[] = $loss;
        }
        // $secondRow[] = $total;
        $table[] = $secondRow;

        $this->data = $table;
    }

    public function buildTree($data, $transactionData, $parentId, $financial_year, $prev_financial_year)
    {
        $tree = [];

        foreach ($data as $row) {
            if ($row->parent_id == $parentId) {
                // Create an array for the current node
                $node = [
                    'id' => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'nature_of_account' => $row->nature_of_account,
                    'entity_type' => $row->entity_type,
                    'behaviour' => $row->behaviour,
                    'context' => $row->context,
                    'status' => $row->status,
                   // 'rows' => []
                ];

                if(!empty($transactionData[$row->ledger_account_id])) {
                    $node[$prev_financial_year] = round($transactionData[$row->ledger_account_id][$prev_financial_year], 2);
                    $node[$financial_year] = round($transactionData[$row->ledger_account_id][$financial_year], 2);                
                } 
                else {
                    $node[$prev_financial_year] = 0.0;
                    $node[$financial_year] = 0.0;
                }
                $node['rows'] = [];

                // Recursively build the child nodes
                $children = $this->buildTree($data, $transactionData, $row->ledger_account_id, $financial_year, $prev_financial_year);
                if (!empty($children)) {
                    $node[$prev_financial_year] = round(array_sum(array_column($children, $prev_financial_year)), 2);
                    $node[$financial_year] = round(array_sum(array_column($children, $financial_year)), 2);                
                    $node['rows'] = $children;
                }else{
                    // $row->ledger_account_id++;
                    $children = $this->buildTree($data, $transactionData, $row->ledger_account_id++, $financial_year, $prev_financial_year);
                }

                // Add the node to the tree
                if(isset($node[$prev_financial_year]) && isset($node[$financial_year])){
                    if((int)$node[$prev_financial_year] != 0 || (int)$node[$financial_year] != 0){
                        $tree[] = $node;
                    }
                }
            }
        }

        return $tree;
    }
}
