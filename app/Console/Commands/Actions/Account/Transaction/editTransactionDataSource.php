<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use Illuminate\Console\Command;
use App\Console\Commands\Action;
use App\Models\Tenants\ChsoneGrpLedgerTree;
use App\Models\Tenants\ChsoneLedgerTransaction;

class editTransactionDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:editTransaction {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Edit Transaction DataSource';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        $request = $this->input;

        // first check transaction entry is exist or not in the database
        $from_txn_id = $request['id'];
        $to_txn_id = $request['to_txn_id'];
        $LedgerData = ChsoneLedgerTransaction::where('txn_id', $from_txn_id)->first();
        $from_ledger_account_name = $LedgerData->ledger_account_name;

        // if transaction is not found then return error
        if (!$LedgerData) {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "No data found for the given transaction id";
            return;
        }

        // now find out toLedgerData from the transaction where txn_from_id = from_txn_id
        $toLedgerData = ChsoneLedgerTransaction::where('txn_id', $to_txn_id)->first();
        $to_ledger_account_name = $toLedgerData->ledger_account_name;

        // now update the transaction
        $from_txn_id = $request['id'];
        $txn_date = $request['transaction_date'];
        $from_ledger_account_name = $from_ledger_account_name;
        $from_ledg_id = $request['from_ledger_id'];
        $txn_amt_from = $request['from_transaction_amount'];
        $txn_type_from = $request['from_transaction_type'];
        $narration_from = $request['memo_desc_from'];
        $from_id = $this->_editTransaction($from_txn_id, $from_ledg_id, $from_ledger_account_name, $txn_amt_from, $narration_from, $txn_date, $txn_type_from);

        $to_txn_id = $request['to_txn_id'];
        $txn_date = $request['transaction_date'];
        $to_ledger_account_name = $request['to_ledger_account_name'];
        $to_ledg_id = $request['to_ledger_id'];
        $txn_amt_to = $request['to_transaction_amount'];
        $txn_type_to = $request['to_transaction_type'];
        $narration_to = $request['to_memo_desc'];
        $to_id = $this->_editTransaction($to_txn_id, $to_ledg_id, $to_ledger_account_name, $txn_amt_to, $narration_to, $txn_date, $txn_type_to);

        $flag = 0;
        if (isset($to_id) && $to_id != '') {
            $flag = 1;
        }
        if (isset($from_id) && $from_id != '') {
            $flag = 1;
        }
        if ($flag == 1) {
            $this->status = "success";
            $this->statusCode = 200;
            $this->message = "Ledger Transaction updated successfully";
        } else {
            $this->status = "error";
            $this->statusCode = 400;
            $this->message = "Error in updating Ledger Transaction";
        }
    }

    public function _editTransaction($txn_id, $ledger_id, $ledger_account_name, $txn_amt, $narration, $txn_date, $txn_type)
    {
        $soc_id = $this->input['company_id'];

         // Find the transaction
        $txn = ChsoneLedgerTransaction::where('soc_id', $soc_id)
        ->where('txn_id', $txn_id)
        ->first();

        if (!$txn) {
            return false; // Return false if no transaction is found
        }

        // Update the transaction details
        $txn->txn_id = $txn_id;
        $txn->transaction_date = $txn_date;
        $txn->ledger_account_id = $ledger_id;
        $txn->transaction_type = $txn_type;
        $txn->transaction_amount = $txn_amt;
        $txn->memo_desc = $narration;
        $txn->ledger_account_name = $ledger_account_name;

        // Save and return the transaction ID or false on failure
        if ($txn->save()) {
            return $txn->txn_id;
        } else {
            return false;
        }
    }
}
