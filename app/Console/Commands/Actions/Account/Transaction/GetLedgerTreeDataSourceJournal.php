<?php
namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use Illuminate\Console\Command;

class GetLedgerTreeDataSourceJournal extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:GetLedgerTreeJournal {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Ledger Tree Data Source';

    /**
     * Execute the console command.
     */

     public function apply()
     {
         $inputType = strtolower($this->input['data'] ?? ''); // Fetch 'data' from input
         $soc_id = $this->input['company_id'];
     
         // Determine which tabs to process based on input
         switch ($inputType) {
             case 'liabilityexpense':
                 $tabsToProcess = ['liability', 'expense'];
                 break;
            case 'income':
                $tabsToProcess = ['income'];
                break;
             default:
                 $tabsToProcess = ['asset', 'liability'];
                 break;
         }
     
         $query = $this->tenantDB()
             ->table('chsone_grp_ledger_tree')
             ->whereIn('nature_of_account', ['dr', 'cr', 'Credit'])
             ->where('soc_id', $soc_id)
             ->where(function ($query) {
                 $query->where('parent_id', 0)
                       ->orWhereNull('parent_id');
             });
     
         $result = $query->get();
     
         // Map ledger_account_name to ID
         $parentIdMap = [];
         foreach ($result as $row) {
             $parentIdMap[strtolower($row->ledger_account_name)] = $row->ledger_account_id;
         }
     
         $finalData = [];
     
         foreach ($tabsToProcess as $tab) {
             $normalizedTab = strtolower(trim($tab));
             $parentId = $parentIdMap[$normalizedTab] ?? null;
     
             if (!$parentId) continue; // skip if not found
     
             // Recursive SQL query
             $condition = "parent_id = $parentId";
             $query = "WITH RECURSIVE LedgerTree AS (
                 SELECT ledger_account_id, ledger_account_name, ledger_account_name as title,
                        nature_of_account, entity_type, behaviour, parent_id, 0 AS level, status, context
                 FROM chsone_grp_ledger_tree
                 WHERE $condition
                 UNION ALL
                 SELECT child.ledger_account_id, child.ledger_account_name, child.ledger_account_name as title,
                        child.nature_of_account, child.entity_type, child.behaviour, child.parent_id,
                        parent.level + 1, child.status, child.context
                 FROM chsone_grp_ledger_tree AS child
                 INNER JOIN LedgerTree AS parent ON child.parent_id = parent.ledger_account_id
             )
             SELECT * FROM LedgerTree ORDER BY level, ledger_account_name;";
     
             $query = $this->filter($query);
             $result = $this->tenantDB()->select($query);
             $tree = $this->buildTree($result, $parentId);
     
             $finalData = array_merge($finalData, $tree);
         }
     
         $this->data = $finalData;
     }
     
     
     
    
    public function buildTree($data, $parentId)
    {
        $tree = [];

        foreach ($data as $row) {
            if ($row->parent_id == $parentId && (! isset($_REQUEST['entity_type']) || ($_REQUEST['entity_type'] == 'group' && $row->entity_type == 'group'))) {
                $node = [
                    'id'                  => $row->ledger_account_id,
                    'ledger_account_name' => $row->ledger_account_name,
                    'title'               => $row->ledger_account_name,
                    'nature_of_account'   => $row->nature_of_account,
                    'entity_type'         => $row->entity_type,
                    'behaviour'           => $row->behaviour,
                    'context'             => $row->context,
                    'status'              => $row->status,
                    'rows'                => $this->buildTree($data, $row->ledger_account_id),
                ];

                $tree[] = $node;
            }
        }

        return $tree;
    }
}
