<?php

namespace App\Console\Commands\Actions\Account\Transaction;

use App\Console\Commands\Action;
use App\Http\Traits\MongoTraits;
use Illuminate\Support\Facades\Config;

class ListTransactionDetailDataSource extends Action
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */


    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    protected $signature = 'datasource:listTransactionDetail {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the voucher reciept';
    use MongoTraits; // Use the MongoTraits trait in this class



    protected $formatterByKeys = [
        'voucher_id',
    ];

    protected $mapper = [];

    /**
     * Execute the console command.
     */

    public function apply()
    {
        // $ledgerId = $this->input['id'];
        // $txn_id = $this->input['txn_id'];

        // $transactions = $this->tenantDB()->table('chsone_ledger_transactions as lTxn')
        //     ->selectRaw('
        //     CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_name ELSE lTxn.ledger_account_name END AS counter_ledger_account_name,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_id ELSE mTxn.txn_id END AS txn_id,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_from_id ELSE mTxn.txn_from_id END AS txn_from_id,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.ledger_account_id ELSE mTxn.ledger_account_id END AS ledger_account_name,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.transaction_type ELSE mTxn.transaction_type END AS transaction_type,
        //     CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_id ELSE lTxn.ledger_account_id END AS counter_ledger_account_id,
        //     lTxn.transaction_amount,
        //     lTxn.memo_desc,
        //     lTxn.transaction_date,
        //     lTxn.soc_id,
        //     lTxn.voucher_reference_id,
        //     lTxn.voucher_reference_number,
        //     lTxn.voucher_type,
        //     lTxn.is_cancelled,
        //     lTxn.is_opening_balance,
        //     lTxn.is_reconciled,
        //     lTxn.payment_mode,
        //     lTxn.payment_reference,
        //     lTxn.other_reference_id,
        //     lTxn.created_by,
        //     lTxn.added_on,
        //     lTxn.value_date
        // ', [$ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId])
        //     ->join('chsone_ledger_transactions as mTxn', 'lTxn.txn_id', '=', 'mTxn.txn_from_id')
        //     ->where(function ($query) use ($ledgerId, $txn_id) {
        //         $query->where('lTxn.ledger_account_id', $ledgerId)
        //             ->orWhere('mTxn.ledger_account_id', $ledgerId);
        //     })
        //     ->where('lTxn.txn_id', '=', $txn_id) // Condition for specific txn_id
        //     ->where('lTxn.is_opening_balance', 0)
        //     ->where('lTxn.transaction_amount', '!=', 0)
        //     ->first();

        // actually id is txn_id and ledger_id is ledger_id
        $txn_id = $this->input['id'];
        $ledgerId = $this->input['ledger_id'];

        $transactions = $this->tenantDB()->table('chsone_ledger_transactions as lTxn')
        ->selectRaw('
            CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_name ELSE lTxn.ledger_account_name END AS counter_ledger_account_name,
            CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_id ELSE mTxn.txn_id END AS txn_id,
            CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.txn_from_id ELSE mTxn.txn_from_id END AS txn_from_id,
            CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.ledger_account_id ELSE mTxn.ledger_account_id END AS ledger_account_name,
            CASE WHEN lTxn.ledger_account_id = ? THEN lTxn.transaction_type ELSE mTxn.transaction_type END AS transaction_type,
            CASE WHEN lTxn.ledger_account_id = ? THEN mTxn.ledger_account_id ELSE lTxn.ledger_account_id END AS counter_ledger_account_id,
            lTxn.transaction_amount,
            lTxn.memo_desc,
            lTxn.transaction_date,
            lTxn.soc_id,
            lTxn.voucher_reference_id,
            lTxn.voucher_reference_number,
            lTxn.voucher_type,
            lTxn.is_cancelled,
            lTxn.is_opening_balance,
            lTxn.is_reconciled,
            lTxn.payment_mode,
            lTxn.payment_reference,
            lTxn.other_reference_id,
            lTxn.created_by,
            lTxn.added_on,
            lTxn.value_date
        ', [$ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId, $ledgerId])
        ->join('chsone_ledger_transactions as mTxn', 'lTxn.txn_id', '=', 'mTxn.txn_from_id')
        ->where(function ($query) use ($ledgerId) {
            $query->where('lTxn.ledger_account_id', $ledgerId)
                ->orWhere('mTxn.ledger_account_id', $ledgerId);
        })
        ->where('lTxn.txn_id', '=', $txn_id) // Condition for specific txn_id
        ->where('lTxn.is_opening_balance', 0)
        ->where('lTxn.transaction_amount', '!=', 0)
        ->first();

        if (!$transactions) {
            $this->message = 'No transactions found';
            $this->status = "error";
            $this->statusCode = 400;
            return;
        }

        $this->data = $transactions;
    }
}
