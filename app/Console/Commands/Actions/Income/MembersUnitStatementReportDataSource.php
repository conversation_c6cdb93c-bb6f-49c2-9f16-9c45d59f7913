<?php

namespace App\Console\Commands\Actions\Income;

use App\Console\Commands\Action;
use App\Models\Tenants\SocAccountFinancialYearMaster;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class MembersUnitStatementReportDataSource extends Action
{

    protected $constants;

    public function __construct()
    {
        parent::__construct();
        $this->constants = Config::get("constants");
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasource:membersUnitStatementReport {flowId} {parentId} {input}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the Member Unit Statement Report Data Source';

    /**
     * Execute the console command.
     */
    public function apply()
    {
        try     {
            $config = Config::get("constants");
    
            $arrUnitStatementDetail = [];
    
            $data = $this->input;
            $data['soc_id'] = $data['company_id'];
            $data['soc_name'] = $this->input['additional_data']['society_name'];
            // Check if 'from_date' is provided, otherwise set it to the first date of the current month
            $data['from_date'] = $data['filters']['startDate'] ?? date('Y-m-01');
    
            // Check if 'to_date' is provided, otherwise set it to the last date of the current month
            $data['to_date'] = $data['filters']['endDate'] ?? date('Y-m-t');
            $data['type'] = $data['filters']['type'] ?? 'both';
            $data['refundable'] = $data['filters']['refundable'] ?? 'yes';
            $data['unit_id'] = $data['filters']['unit_id'] ?? null;

            // check if unit_id is all then fetch the unit_id from chsone_units_master table and pass unit_id as array
            if ($data['unit_id'] == 'all') {
                $unit_id = $this->tenantDB()->table('chsone_units_master')->pluck('unit_id')->toArray();
                $data['unit_id'] = $unit_id;
            }
    
            $arrUnitStatementDetail = $this->getUnitStatementDetail($data);
            $arrUnitStatementDetail = json_decode(json_encode($arrUnitStatementDetail), true);
    
            $report = $this->formatUnitStatementDetailReport(array('arrUnitStatementDetail' => $arrUnitStatementDetail));
            $report = json_decode(json_encode($report), true);
            $this->data = $report;
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function getUnitStatementDetail($data)
    {
        $arrUnitStatementDetail = [];
        $arrIncidentDetail = [];
        $arrIncidentInterestDetail = [];

        if (!empty($data['unit_id'])) {
            // fetch first financial year of society
            $arrCurrentFYDetail = $this->getCurrentFinancialYear(array('soc_id' => $data['soc_id']));
            $current_fy_start_date = $arrCurrentFYDetail->fy_start_date->format('Y-m-d');

            $data['date'] = $data['from_date'];
            $opening = $this->getOpeningBalance($data);
            $opening = json_decode(json_encode($opening), true);

            // Check if $opening is countable before calling count()
            if (!empty($opening) && count($opening) > 0 && (strtotime($data['from_date']) <= strtotime($data['to_date']))) {
                // Prepare the key for the opening statement detail array
                //$key = '1-' . $data['from_date'];

                // Populate the opening statement details with the required data
                // $openingStatementDetail[$key] = [
                //     'date' => $data['from_date'],
                //     'type' => $data['type'],
                //     'reference_id' => 'Opening Balance',
                //     'payment_reference' => '',
                //     'display_type' => 'Opening Balance',
                //     'amount' => $opening[0]['amount'],
                //     'nature' => $opening[0]['ledger_nature'],
                // ];
                $openingStatementDetail['1-' . $data['from_date']]['date'] = $data['from_date'];
                $openingStatementDetail['1-' . $data['from_date']]['type'] = "";
                $openingStatementDetail['1-' . $data['from_date']]['reference_id'] = 'Opening Balance';
                $openingStatementDetail['1-' . $data['from_date']]['payment_reference'] = '';
                $openingStatementDetail['1-' . $data['from_date']]['display_type'] = 'Opening Balance';
                $openingStatementDetail['1-' . $data['from_date']]['amount'] = $opening[0]['amount'] ?? 0;
                $openingStatementDetail['1-' . $data['from_date']]['nature'] = $opening[0]['ledger_nature'] ?? '';
            }

            // if type = incident and $currentFYDetail['fy_start_date'] is equal to $data['start_date'] then set empty opening balance
            if ($data['type'] == 'incident' && $current_fy_start_date == $data['from_date']) {
                $openingStatementDetail['1-' . $data['from_date']]['date'] = $data['from_date'];
                $openingStatementDetail['1-' . $data['from_date']]['type'] = "";
                $openingStatementDetail['1-' . $data['from_date']]['reference_id'] = 'Opening Balance';
                $openingStatementDetail['1-' . $data['from_date']]['payment_reference'] = '';
                $openingStatementDetail['1-' . $data['from_date']]['display_type'] = 'Opening Balance';
                $openingStatementDetail['1-' . $data['from_date']]['amount'] = '';
                $openingStatementDetail['1-' . $data['from_date']]['nature'] = '';
            }

            // Get maintenance invoice detail
            if (in_array(strtolower($data['type']), ['both', 'maintenance'])) {
                $arrUnitInvoiceDetail = $this->getMaintenanceInvoiceDetailByDateRange($data);
                $arrUnitInvoiceDetail = json_decode(json_encode($arrUnitInvoiceDetail), true);

                if (!empty($arrUnitInvoiceDetail)) {

                    $arrInvoiceTaxDetail = $this->getInvoiceTaxAmountByDateRange($data);

                    $arrFirstInvoiceDetail = $this->getFirstMaintenanceInvoiceDetail($data);

                    // Initialize multiple arrays in a clean and readable manner
                    $arrOutstandingDetail = [];
                    $arrMaintenanceDetail = [];
                    $arrAllReceiptDetail = [];
                    // $arrAllReceiptDetail = json_decode(json_encode($arrAllReceiptDetail), true);
                  
                    $arrIncidentInterestDetail = [];

                    $k = 0;
                    foreach ($arrUnitInvoiceDetail as $eachInvoiceDetail) {
                        $totalAmount = 0;
                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['date'] = $eachInvoiceDetail['created_dt'];
                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['type'] = 'invoice';
                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['reference_id'] = $eachInvoiceDetail['invoice_number'];
                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['payment_reference'] = '';
                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['display_type'] = 'Maintenance Invoice';
                        
                        $totalAmount = $eachInvoiceDetail['invoice_amount'] + $eachInvoiceDetail['interest_amount'] + $eachInvoiceDetail['roundoff_amount'];
                        if (!empty($arrInvoiceTaxDetail[$eachInvoiceDetail['invoice_number']]['tax_amount']) && $arrInvoiceTaxDetail[$eachInvoiceDetail['invoice_number']]['tax_amount'] > 0) {
                            $totalAmount += $arrInvoiceTaxDetail[$eachInvoiceDetail['invoice_number']]['tax_amount'];
                        }
                        if (isset($arrFirstInvoiceDetail[$eachInvoiceDetail['invoice_number']]) && !empty($arrFirstInvoiceDetail[$eachInvoiceDetail['invoice_number']])) {
                            if (!empty($eachInvoiceDetail['advance_amount']) && $eachInvoiceDetail['advance_amount'] > 0) {
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['date'] = $eachInvoiceDetail['from_date'];
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['type'] = 'Arrear';
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['reference_id'] = 'Opening Balance';
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['payment_reference'] = '';
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['display_type'] = 'Opening Balance';
                                // $arrOutstandingDetail[$eachInvoiceDetail['from_date']]['amount'] = $eachInvoiceDetail['advance_amount'];
                                //$totalAmount = ($eachInvoiceDetail['advance_amount']>=$totalAmount) ? 0 : (float)round($totalAmount-$eachInvoiceDetail['advance_amount'],3);
                            } elseif ($eachInvoiceDetail['principal_amount'] > 0) {
                                //   $totalAmount += $arrFirstInvoiceDetail[$eachInvoiceDetail['invoice_number']]['principal_amount'];
                            }

                            if ($arrFirstInvoiceDetail[$eachInvoiceDetail['invoice_number']]['interest_amount'] > 0) {
                                $totalAmount -= $arrFirstInvoiceDetail[$eachInvoiceDetail['invoice_number']]['interest_amount'];
                            }
                        }

                        $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]['amount'] = $totalAmount;
                        if (!empty($arrOutstandingDetail)) {
                            $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-A'] = $arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k];
                            unset($arrMaintenanceDetail[$eachInvoiceDetail['created_dt'] . '-m' . $k]);
                        }
                        $k++;
                    }

                    $arrUnitStatementDetail = (!empty($arrOutstandingDetail) && count($arrOutstandingDetail) > 0) ? array_merge($arrOutstandingDetail, $arrMaintenanceDetail) : $arrMaintenanceDetail;
                }
            }

            // Define bill types based on the report type
            $arrBillType = [];
            if(strtolower($data['type']) == 'maintenance'){
                $arrBillType = ['member', 'creditaccount-member', 'member_common_bill'];
            }elseif (strtolower($data['type']) == 'incident'){
                $arrBillType = ['common_bill', 'common_bill_quickpay', 'creditaccount-member', 'member_common_bill'];
            }

            // Get payment receipt details
            // Prepare the data array for the getInvoicePaymentTracker method call
            $params = [
                'soc_id' => $data['soc_id'],
                'searchData' => [
                    'payment_status' => ['y'],
                    'unit_id' => $data['unit_id'],
                    'bill_type' => $arrBillType,
                ],
                'reportDateRange' => [
                    'from_date' => $data['from_date'],
                    'to_date' => $data['to_date'],
                ],
            ];

            // Call the getInvoicePaymentTracker method with the prepared parameters
            $arrReceiptDetail = $this->getInvoicePaymentTracker($params);

            if (!empty($arrReceiptDetail)) {
                $arrCreditReceiptKey = [];

                foreach ($arrReceiptDetail as $eachReceipt) {
                    $eachReceipt = json_decode(json_encode($eachReceipt), true);
                    $receiptKey = $eachReceipt['payment_date'];
                    $tempReceiptDetail = array();
                    $tempReceiptDetail['date'] = $eachReceipt['payment_date'];
                    $tempReceiptDetail['type'] = 'receipt';
                    $tempReceiptDetail['reference_id'] = $eachReceipt['receipt_number'];
                    $tempReceiptDetail['payment_reference'] = ($eachReceipt['payment_mode'] == 'cashtransfer') ? DISPLAY_CASH_TRANSFER : $eachReceipt['payment_mode'];
                    if (!empty($eachReceipt['payment_mode']) && strtolower($eachReceipt['payment_mode']) != 'cash') {
                        $tempReceiptDetail['payment_reference'] .= ' / ' . trim($eachReceipt['transaction_reference']);
                    }
                    if (!empty($eachReceipt['payment_mode']) && strtolower($eachReceipt['payment_mode']) == 'cheque') {
                        $tempReceiptDetail['payment_reference'] .= ' / ' . trim($eachReceipt['payment_instrument']);
                    }
                    if (!empty($eachReceipt['bill_type']) && in_array(strtolower($eachReceipt['bill_type']), array('member'))) {
                        $tempReceiptDetail['display_type'] = 'Maintenance Receipt';
                    } elseif (!empty($eachReceipt['bill_type']) && in_array(strtolower($eachReceipt['bill_type']), array('common_bill', 'common_bill_quickpay'))) {
                        $tempReceiptDetail['display_type'] = 'Incident Receipt';
                    } elseif (!empty($eachReceipt['bill_type']) && in_array(strtolower($eachReceipt['bill_type']), array('creditaccount-member'))) {
                        $tempReceiptDetail['display_type'] = 'Advance Receipt';
                        $tempReceiptDetail['payment_tracker_id'] = $eachReceipt['payment_tracker_id'];
                        array_push($arrCreditReceiptKey, $eachReceipt['payment_tracker_id']);
                    } elseif (!empty($eachReceipt['bill_type']) && in_array(strtolower($eachReceipt['bill_type']), array('member_common_bill'))) {
                        $tempReceiptDetail['display_type'] = 'Maintenance/Incidental Receipt';
                    }
                    $tempReceiptDetail['amount'] = (!empty($eachReceipt['tds_deducted']) && $eachReceipt['tds_deducted'] > 0) ? (float) round($eachReceipt['payment_amount'] - $eachReceipt['tds_deducted'], 2) : $eachReceipt['payment_amount'];
                    if ((!empty($arrMaintenanceDetail) && array_key_exists($receiptKey, $arrMaintenanceDetail)) || (!empty($arrAllReceiptDetail) && array_key_exists($receiptKey, $arrAllReceiptDetail))) {
                        $receiptKey = $eachReceipt['payment_date'] . '-R' . $eachReceipt['payment_tracker_id'];
                    }
                    $arrAllReceiptDetail[$receiptKey] = $tempReceiptDetail;
                    //Create Writeoff row
                    if (!empty($eachReceipt['writeoff_amount']) && $eachReceipt['writeoff_amount'] > 0) {
                        $tempReceiptDetail['type'] = 'writeoff';
                        $tempReceiptDetail['payment_reference'] = '';
                        $tempReceiptDetail['amount'] = $eachReceipt['writeoff_amount'];
                        $arrAllReceiptDetail[$eachReceipt['payment_date'] . '-W' . $eachReceipt['payment_tracker_id']] = $tempReceiptDetail;
                    }
                    //Create TDS row
                    if (!empty($eachReceipt['tds_deducted']) && $eachReceipt['tds_deducted'] > 0) {
                        $tempReceiptDetail['type'] = 'TDS';
                        $tempReceiptDetail['payment_reference'] = '';
                        $tempReceiptDetail['amount'] = $eachReceipt['tds_deducted'];
                        $arrAllReceiptDetail[$eachReceipt['payment_date'] . '-T' . $eachReceipt['payment_tracker_id']] = $tempReceiptDetail;
                    }

                }

                $arrUnitStatementDetail = (!empty($arrAllReceiptDetail) && count($arrAllReceiptDetail) > 0) ? array_merge($arrUnitStatementDetail, $arrAllReceiptDetail) : $arrUnitStatementDetail;

                // Fetch credit details if there are credit receipt keys
                if (!empty($arrCreditReceiptKey)) {
                    $arrCreditDetail = $this->getUnitCreditByTrackerId([
                        'soc_id' => $data['soc_id'],
                        'unit_id' => $data['unit_id'],
                        'payment_tracker_id' => $arrCreditReceiptKey,
                    ]);

                    if (!empty($arrCreditDetail)) {
                        $tempArrUnitStatementDetail = $arrUnitStatementDetail;
                        foreach ($arrCreditDetail as $eachCreditDetail) {
                            foreach ($tempArrUnitStatementDetail as $key => $eachUnitDetail) {
                                $eachUnitDetail = json_decode(json_encode($eachUnitDetail), true);
                                $eachCreditDetail = json_decode(json_encode($eachCreditDetail), true);
                                // if(empty($tempReceiptDetail['to_by'])){
                                //     $arrUnitStatementDetail[$key]['to_by'] = $eachUnitDetail['account_name'];

                                // }

                                if (isset($eachUnitDetail['payment_tracker_id']) && $eachUnitDetail['payment_tracker_id'] == $eachCreditDetail['payment_tracker_id']) {
                                    $arrUnitStatementDetail[$key]['display_type'] = 'Adv-' . ucfirst($eachCreditDetail['use_credit']) . ' Receipt';
                                    if (strtolower($data['type']) == 'maintenance' && strtolower($eachCreditDetail['use_credit_for']) == 'incidental') {
                                        unset($arrUnitStatementDetail[$key]);
                                    } elseif (strtolower($data['type']) == 'incident' && strtolower($eachCreditDetail['use_credit_for']) == 'maintenance') {
                                        unset($arrUnitStatementDetail[$key]);
                                    }
                                    //Refundable filter
                                    if (!empty($eachCreditDetail['use_credit']) && strtolower($eachCreditDetail['use_credit']) == 'refundable' && strtolower($data['refundable']) == 'no') {
                                        //echo '<pre>REF:'.$key;print_r($eachCreditDetail);
                                        unset($arrUnitStatementDetail[$key]); //exit;
                                    }
                                }
                            }
                        }
                    }
                }

                // Fetch credit refunds and add to statement details if applicable
                $arrCreditRefundsDetail = $this->getUnitCreditRefunds([
                    'soc_id' => $data['soc_id'],
                    'unit_id' => $data['unit_id'],
                ]);

                if (!empty($arrCreditRefundsDetail)) {
                    foreach ($arrCreditRefundsDetail as $eachRefundDetail) {

                        $eachRefundDetail = json_decode(json_encode($eachRefundDetail), true);
                        if (!empty($eachRefundDetail['use_credit']) && strtolower($eachRefundDetail['use_credit']) == 'refundable' && strtolower($data['refundable']) == 'yes' && strtolower($eachRefundDetail['transaction_type']) == 'dr' && (strtotime($eachRefundDetail['payment_date']) >= strtotime($data['from_date'])) && (strtotime($eachRefundDetail['payment_date']) <= strtotime($data['to_date']))) {
                            //echo '<pre>REF:'.$key;print_r($eachCreditDetail);
                            $newStatementItem = [];
                            $newStatementItem['date'] = $eachRefundDetail['payment_date'];
                            $newStatementItem['type'] = 'refund';
                            $newStatementItem['reference_id'] = '';
                            $newStatementItem['amount'] = $eachRefundDetail['amount'];
                            $newStatementItem['display_type'] = 'Refund Advance';

                            $narration = $eachRefundDetail['narration'];

                            if (strpos($narration, 'through') !== false) {
                                $narrationArr = explode('through', $narration);
                            } elseif (strpos($narration, 'via') !== false) {
                                $narrationArr = explode('via', $narration);
                            } else {
                                // Optional fallback if neither "through" nor "via" is present
                                $narrationArr = [$narration];
                            }

                                $ref = explode("on", $narrationArr[1])[0];
                                $ref = str_replace("(", " / ", $ref);
                                $ref = str_replace(")", " ", $ref);
                                $newStatementItem['payment_reference'] = $ref;

                            $newStatementItem['nature'] = 'dr';

                            $arrUnitStatementDetail[$eachRefundDetail['payment_date'] . '-RE'] = $newStatementItem; //exit;
                        }
                    }
                }

            }

            // Get incident invoice details
            if (in_array(strtolower($data['type']), ['both', 'incident'])) {
                $arrIncidentInvoiceDetail = $this->getIncidentInvoiceDetailByDateRange($data);
                $arrIncidentInvoiceDetail = json_decode(json_encode($arrIncidentInvoiceDetail), true);

                if (!empty($arrIncidentInvoiceDetail)) {
                    foreach ($arrIncidentInvoiceDetail as $eachIncidentInvoice) {
                        $incidentKey = $eachIncidentInvoice['created_dt'];
                        if ((!empty($arrUnitStatementDetail) && array_key_exists($incidentKey, $arrUnitStatementDetail)) || (!empty($arrIncidentDetail) && array_key_exists($incidentKey, $arrIncidentDetail))) {
                            $incidentKey .= '-I' . $eachIncidentInvoice['id'];
                        }
                        $arrIncidentDetail[$incidentKey]['date'] = $eachIncidentInvoice['created_dt'];
                        $arrIncidentDetail[$incidentKey]['type'] = 'invoice';
                        $arrIncidentDetail[$incidentKey]['reference_id'] = $eachIncidentInvoice['invoice_number'];
                        $arrIncidentDetail[$incidentKey]['payment_reference'] = '';
                        //$arrIncidentDetail[$incidentKey]['to_by'] = $eachIncidentInvoice['member_name'];
                        $arrIncidentDetail[$incidentKey]['display_type'] = 'Incidental Invoice';
                        $totalAmount = $eachIncidentInvoice['amount'];
                        if (!empty($eachIncidentInvoice['tax_amount']) && $eachIncidentInvoice['tax_amount'] > 0) {
                            $totalAmount += $eachIncidentInvoice['tax_amount'];
                        }
                        $arrIncidentDetail[$incidentKey]['amount'] = $totalAmount;
                    }
                    $arrUnitStatementDetail = (!empty($arrIncidentDetail) && count($arrIncidentDetail) > 0) ? array_merge($arrUnitStatementDetail, $arrIncidentDetail) : $arrUnitStatementDetail;
                }

                // Start incidental interest amount fetching
                $IncidentInvoiceInterestDetail = $this->getIncidentInvoiceInterestDetailByDateRange($data);
                $IncidentInvoiceInterestDetail = json_decode(json_encode($IncidentInvoiceInterestDetail), true);
                if (!empty($IncidentInvoiceInterestDetail)) {
                    foreach ($IncidentInvoiceInterestDetail as $eachIncidentInvoice) {
                        $incidentKey = $eachIncidentInvoice['created_dt'];
                        if ((!empty($arrUnitStatementDetail) && array_key_exists($incidentKey, $arrUnitStatementDetail)) || (!empty($arrIncidentInterestDetail) && array_key_exists($incidentKey, $arrIncidentInterestDetail))) {
                            $incidentKey .= '-I' . $eachIncidentInvoice['id'];
                        }
                        $arrIncidentInterestDetail[$incidentKey]['date'] = $eachIncidentInvoice['created_dt'];
                        $arrIncidentInterestDetail[$incidentKey]['type'] = 'invoice';
                        $arrIncidentInterestDetail[$incidentKey]['reference_id'] = $eachIncidentInvoice['invoice_number'];
                        $arrIncidentInterestDetail[$incidentKey]['payment_reference'] = '';
                        $arrIncidentInterestDetail[$incidentKey]['display_type'] = 'Incidental Invoice Interest';
                        // need to fetch round off amount from chsone_ledger_transcation table
                        $round_off = 0;
                        $roundOffAmount = $this->getRoundOffIncidentInvoice($eachIncidentInvoice);
                        $roundOffAmount = json_decode(json_encode($roundOffAmount), true);
                        if (!empty($roundOffAmount)) {
                            $round_off = $roundOffAmount[0]['transaction_amount'];
                        }
                        $arrIncidentInterestDetail[$incidentKey]['amount'] = $eachIncidentInvoice['interest_amount'] + $round_off;
                    }
                    $arrUnitStatementDetail = (!empty($arrIncidentInterestDetail) && count($arrIncidentInterestDetail) > 0) ? array_merge($arrUnitStatementDetail, $arrIncidentInterestDetail) : $arrUnitStatementDetail;
                }
                // End incidental interest amount fetching
            }

            // Get voucher details
            $arrVoucherDetail = $this->getVoucherDetails($data);
            $arrVoucherDetail = json_decode(json_encode($arrVoucherDetail), true);

            if (!empty($arrVoucherDetail)) {
                $arrUnitVoucherDetails = array();

                foreach ($arrVoucherDetail as $unitVoucher) {
                    $vkey = $unitVoucher['transaction_date'] . '-v-' . $unitVoucher['id'];
                    $arrUnitVoucherDetails[$vkey]['date'] = $unitVoucher['transaction_date'];
                    $arrUnitVoucherDetails[$vkey]['type'] = $unitVoucher['type'];
                    $arrUnitVoucherDetails[$vkey]['reference_id'] = (($unitVoucher['nature'] == 'dr') ? "TO " . $unitVoucher['to_ledger_account_name'] : "FROM " . $unitVoucher['from_ledger_account_name']);
                    $arrUnitVoucherDetails[$vkey]['payment_reference'] = $unitVoucher['reference'];
                    //$arrUnitVoucherDetails[$vkey]['to_by'] = $unitVoucher['member_name'];
                    $arrUnitVoucherDetails[$vkey]['display_type'] = "Voucher";
                    $arrUnitVoucherDetails[$vkey]['amount'] = $unitVoucher['amount'];
                    $arrUnitVoucherDetails[$vkey]['nature'] = $unitVoucher['nature'];
                }
                $arrUnitStatementDetail = (!empty($arrUnitVoucherDetails) && count($arrUnitVoucherDetails) > 0) ? array_merge($arrUnitStatementDetail, $arrUnitVoucherDetails) : $arrUnitStatementDetail;
            }

            // Get credit note details
            $arrCreditNoteDetail = $this->getCreditNoteDetails($data);

            if (!empty($arrCreditNoteDetail)) {
                //$arrCreditNoteDetail = array();

                foreach ($arrCreditNoteDetail as $unitVoucher) {
                    $vkey = $unitVoucher->payment_date . '-c-' . $unitVoucher->credit_id;
                    $arrUnitCreditNoteDetails[$vkey]['date'] = $unitVoucher->payment_date;
                    $arrUnitCreditNoteDetails[$vkey]['type'] = "Credit Note";
                    $arrUnitCreditNoteDetails[$vkey]['reference_id'] = $unitVoucher->credit_id . (empty($unitVoucher->credit_type) ? "" : ("(" . ucfirst($unitVoucher->credit_type) . ")"));
                    $arrUnitCreditNoteDetails[$vkey]['payment_reference'] = "Credit Note";
                    //$arrUnitCreditNoteDetails[$vkey]['to_by'] = $unitVoucher['account_name']?$unitVoucher['account_name']:"";
                    $arrUnitCreditNoteDetails[$vkey]['display_type'] = "Credit Note";
                    $arrUnitCreditNoteDetails[$vkey]['amount'] = $unitVoucher->amount;
                    $arrUnitCreditNoteDetails[$vkey]['nature'] = "cr";
                }
                $arrUnitStatementDetail = (!empty($arrUnitCreditNoteDetails) && count($arrUnitCreditNoteDetails) > 0) ? array_merge($arrUnitStatementDetail, $arrUnitCreditNoteDetails) : $arrUnitStatementDetail;
            }

            $first = $this->getFirstMaintenanceInvoiceDetail($data);

            // Combine with opening statement details if applicable
            if (count($first) || count($arrIncidentDetail) || count($arrIncidentInterestDetail)) {
                $arrUnitStatementDetail = (!empty($openingStatementDetail) && count($openingStatementDetail) > 0) ? array_merge($arrUnitStatementDetail, $openingStatementDetail) : $arrUnitStatementDetail;
            }

            if (!empty($arrUnitStatementDetail)) {
                ksort($arrUnitStatementDetail);
            }
        }

        return $arrUnitStatementDetail;
    }

    public function getCurrentFinancialYear($data=array()){
       
        $arrFYDetail = array();
        $arrFYDetail = SocAccountFinancialYearMaster::where('soc_id', $data['soc_id'])
        ->where('confirmed', 0)
        ->orderBy('account_closing_id', 'asc')
        ->first();

        return $arrFYDetail;
    }

    public function getOpeningBalance(array $data = []): array
    {
        $openingBalance = [];

        // Validate the required inputs
        if (
            (!empty($data['soc_id']) && is_numeric($data['soc_id'])) &&
            ((!empty($data['ledger_id']) && is_numeric($data['ledger_id'])) ||
                (!empty($data['unit_id']) && is_numeric($data['unit_id'])))
        ) {
            $socId = $data['soc_id'];
            $ledgerID = $this->getLedgerId($data, $socId);

            $date = $data['date'] ?? null;

            if (empty($date)) {
                $this->fetchBalanceWithoutDate($data, $socId, $ledgerID, $openingBalance);
            } else {
                $this->fetchBalanceWithDate($data, $socId, $ledgerID, $date, $openingBalance);
            }
        }

        // Default opening balance if none found
        if (empty($openingBalance)) {
            $openingBalance[] = ['amount' => 0, 'ledger_nature' => 'cr'];
        }

        return $openingBalance;
    }

    /**
     * Fetch ledger ID.
     *
     * @param array $data
     * @param int $socId
     * @return int|string
     */
    private function getLedgerId(array $data, int $socId)
    {
        return (!empty($data['ledger_id']) && is_numeric($data['ledger_id']))
        ? $data['ledger_id']
        : $this->tenantDB()->table('chsone_units_master')
            ->where('soc_id', $socId)
            ->where('unit_id', $data['unit_id'])
            ->value('ledger_account_id');
    }

    /**
     * Fetch ledger nature.
     *
     * @param array $data
     * @param int $socId
     * @return string
     */
    private function getLedgerNature(array $data, int $socId)
    {
        return (!empty($data['ledger_id']) && is_numeric($data['ledger_id']))
        ? $this->tenantDB()->table('chsone_grp_ledger_tree')
            ->where('soc_id', $socId)
            ->where('ledger_account_id', $data['ledger_id'])
            ->value('nature_of_account')
        : '';
    }

    /**
     * Fetch opening balance without a specific date.
     *
     * @param array $data
     * @param int $socId
     * @param int|string $ledgerID
     * @param array &$openingBalance
     */
    private function fetchBalanceWithoutDate(array $data, int $socId, $ledgerID, array &$openingBalance)
    {
        if (!empty($data['year']) && checkdate(1, 1, $data['year'])) {
            $financialDate = $this->tenantDB()->table('soc_account_financial_year_master')
                ->select('fy_start_date AS from_date', 'fy_end_date AS to_date')
                ->whereRaw('YEAR(fy_start_date) = ?', [$data['year']])
                ->where('soc_id', $socId)
                ->first();
            if ($financialDate) {
                $query = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->select('transaction_amount AS amount', 'transaction_type AS ledger_nature')
                    ->where('soc_id', $socId)
                    ->where('ledger_account_id', $ledgerID)
                    // ->whereBetween('transaction_date', [$financialDate->from_date, $financialDate->to_date])
                    ->where('transaction_date', '>=', $financialDate->from_date)
                    ->where('transaction_date', '<=', $financialDate->to_date)
                    ->where('is_opening_balance', '!=', 0)
                    ->limit(1)
                    ->get();


                $openingBalance = $query->toArray();

            }
        }
    }

    /**
     * Fetch opening balance with a specific date.
     *
     * @param array $data
     * @param int $socId
     * @param int|string $ledgerID
     * @param string $date
     * @param array &$openingBalance
     */
    private function fetchBalanceWithDate(array $data, int $socId, $ledgerID, string $date, array &$openingBalance)
    {
        $ledgerNature = $this->getLedgerNature(array('ledger_id' => $ledgerID), $socId);

        $financialDate = $this->tenantDB()->table('soc_account_financial_year_master')
            ->select('fy_start_date', 'fy_end_date')
            ->whereRaw('? BETWEEN fy_start_date AND fy_end_date', [$date])
            ->where('soc_id', $socId)
            ->first();

        if ($financialDate) {
            $startDate = $financialDate->fy_start_date;
            $endDate = $financialDate->fy_end_date;

            if ($startDate == $date) {
                $openingBalance = $this->tenantDB()->table('chsone_ledger_transactions')
                    ->select('transaction_amount AS amount', 'transaction_type AS ledger_nature')
                    ->where('soc_id', $socId)
                    ->where('ledger_account_id', $ledgerID)
                    // ->whereBetween('transaction_date', [$startDate, $endDate])
                    ->where('transaction_date', '>=', $startDate)
                    ->where('transaction_date', '<=', $endDate)
                    ->where('is_opening_balance', '!=', 0)
                    ->limit(1)
                    ->get()
                    ->toArray();
            } else {
                // check the type if unit statement report
                if ($data['type'] == 'incident') {

                    // check first incident invoice entry in chsone_ledger_transactions with ledger_account_id
                    $invoiceDetails = $this->tenantDB()->table('chsone_ledger_transactions')
                        ->select('*')
                        ->where('soc_id', $socId)
                        ->where('ledger_account_id', $ledgerID)
                        ->where('voucher_reference_number', 'like', 'INC%')
                        ->limit(1)
                        ->first();

                    // if check invoice transaction_date is with start date
                    if (!empty($invoiceDetails) && $invoiceDetails->transaction_date != $date) {
                        $openingBalance = array('amount' => 0, 'ledger_nature' => $ledgerNature);
                    } else {
                        $previousDate = Carbon::parse($date)->subDay()->format('Y-m-d');
                        $checkFunction = $this->tenantDB()->select("SHOW FUNCTION STATUS WHERE Db = DATABASE() AND Type = 'FUNCTION' AND Name = 'ledgerAmountCalByDate'");
                        if(strtotime($startDate) <= strtotime($previousDate) && $checkFunction ) {
                            $endDate = $previousDate;
                            $query = $this->tenantDB()->select("
                            SELECT
                                getPositiveAmount(@amount := ledgerAmountCalByDate(?, L.ledger_account_id, ?, ?)) as amount,
                                getTransType(L.ledger_account_id, L.nature_of_account, @amount) AS ledger_nature
                            FROM chsone_grp_ledger_tree AS L
                            WHERE L.ledger_account_id = ?
                            AND soc_id = ?", [$socId, $startDate, $endDate, $ledgerID, $socId]);

                            $openingBalance = $query;
                            // echo '<pre>'; print_r($openingBalance); exit;
                        }
                    }
                } else {
                    $previousDate = Carbon::parse($date)->subDay()->format('Y-m-d');
                    $checkFunction = $this->tenantDB()->select("SHOW FUNCTION STATUS WHERE Db = DATABASE() AND Type = 'FUNCTION' AND Name = 'ledgerAmountCalByDate'");

                    if (strtotime($startDate) <= strtotime($previousDate) && $checkFunction) {
                        $endDate = $previousDate;
                        $query = $this->tenantDB()->select("
                            SELECT
                                getPositiveAmount(@amount := ledgerAmountCalByDate(?, L.ledger_account_id, ?, ?)) as amount,
                                getTransType(L.ledger_account_id, L.nature_of_account, @amount) AS ledger_nature
                            FROM chsone_grp_ledger_tree AS L
                            WHERE L.ledger_account_id = ?
                            AND soc_id = ?", [$socId, $startDate, $endDate, $ledgerID, $socId]);

                        $openingBalance = $query;
                    }
                }

                // $previousDate = Carbon::parse($date)->subDay()->format('Y-m-d');
                // $checkFunction = $this->tenantDB()->select("SHOW FUNCTION STATUS WHERE Db = DATABASE() AND Type = 'FUNCTION' AND Name = 'ledgerAmountCalByDate'");

                // if (strtotime($startDate) <= strtotime($previousDate) && $checkFunction) {
                //     $endDate = $previousDate;
                //     $query = $this->tenantDB()->select("
                //         SELECT
                //             getPositiveAmount(@amount := ledgerAmountCalByDate(?, L.ledger_account_id, ?, ?)) as amount,
                //             getTransType(L.ledger_account_id, L.nature_of_account, @amount) AS ledger_nature
                //         FROM chsone_grp_ledger_tree AS L
                //         WHERE L.ledger_account_id = ?
                //         AND soc_id = ?", [$socId, $startDate, $endDate, $ledgerID, $socId]);

                //     $openingBalance = $query;
                // }
            }
        }
    }


    /**
     * Get invoice tax amount by date range.
     *
     * @param array $data
     * @return array
     */
    public function getInvoiceTaxAmountByDateRange($data = [])
    {
        $arrMemberTaxDetail = [];

        if (!empty($data['soc_id'])) {
            $queryBuilder = $this->tenantDB()->table('income_unit_invoices AS I')
                ->select(
                    'I.fk_unit_id',
                    'I.soc_building_name',
                    'I.unit_name',
                    'I.bill_to',
                    'I.invoice_number',
                    $this->tenantDB()->raw('SUM(T.tax_amount) as tax_amount'),
                    'I.principal_amount',
                    'I.interest_amount',
                    'I.advance_amount',
                    'I.outstanding_principal',
                    'I.outstanding_interest',
                    'I.roundoff_amount',
                    'I.from_date',
                    'I.to_date'
                )
                ->leftJoin('chsone_tax_log AS T', 'I.invoice_number', '=', 'T.invoice_number')
                ->where('I.soc_id', $data['soc_id'])
                ->whereBetween('I.from_date', [$data['from_date'], $data['to_date']])
                ->where('I.status', '!=', 'cancelled');
            
            // check if unit_id is an array and has values then set where in condition
            if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                $queryBuilder->whereIn('I.fk_unit_id', $data['unit_id']);
            } elseif (isset($data['unit_id']) && is_numeric($data['unit_id'])) {
                // if unit_id is a single numeric value, set where condition
                $queryBuilder->where('I.fk_unit_id', $data['unit_id']);
            } else {
                // if unit_id is 0, set where condition to exclude 0
                $queryBuilder->where('I.fk_unit_id', '!=', 0);
            }
            // if (!empty($data['unit_id'])) {
            //     $queryBuilder->where('I.fk_unit_id', $data['unit_id']);
            // } else {
            //     $queryBuilder->where('I.fk_unit_id', '!=', 0);
            // }

            $queryBuilder->groupBy('I.invoice_number');

            $resultset = $queryBuilder->get();

            if (!$resultset->isEmpty()) {
                foreach ($resultset as $eachInvoiceDetail) {
                    $arrMemberTaxDetail[$eachInvoiceDetail->invoice_number] = (array) $eachInvoiceDetail;
                }
            }
        }

        return $arrMemberTaxDetail;
    }


    /**
     * Get the first maintenance invoice detail.
     *
     * @param array $data
     * @return array
     */
    public function getFirstMaintenanceInvoiceDetail($data = [])
    {
        $arrInvoiceDetailFinal = [];

        if (!empty($data['soc_id'])) {
            $obj = $this->tenantDB()->table('income_unit_invoices AS I')
                ->select(
                    'I.invoice_number',
                    'A.delayed_payment_charges',
                    'A.interest_amount',
                    'A.principal_amount'
                )
                ->leftJoin('income_invoice_adjustment AS A', 'I.fk_unit_id', '=', 'A.fk_unit_id')
                ->where('I.soc_id', $data['soc_id'])
                ->where('I.status', '!=', 'cancelled')
                ->where('A.bill_type', 'maintenance');
            
            // check if unit_id is an array and has values then set where in condition
            if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                $obj = $obj->whereIn('I.fk_unit_id', $data['unit_id']);
            } elseif (isset($data['unit_id']) && is_numeric($data['unit_id'])) {
                // if unit_id is a single numeric value, set where condition
                $obj = $obj->where('I.fk_unit_id', $data['unit_id']);
            } else {
                // if unit_id is 0, set where condition to exclude 0
                $obj = $obj->where('I.fk_unit_id', '!=', 0);
            }
            // if (!empty($data['unit_id'])) {
            //     $obj = $obj->where('I.fk_unit_id', $data['unit_id']);
            // } else {
            //     $obj = $obj->where('I.fk_unit_id', '!=', 0);
            // }

            $obj = $obj->groupBy('I.fk_unit_id');

            $resultset = $obj->get();

            if (!$resultset->isEmpty()) {
                foreach ($resultset as $eachInvoiceDetail) {
                    $arrInvoiceDetailFinal[$eachInvoiceDetail->invoice_number] = [
                        'invoice_number' => $eachInvoiceDetail->invoice_number,
                        'delayed_payment_charges' => $eachInvoiceDetail->delayed_payment_charges,
                        'interest_amount' => $eachInvoiceDetail->interest_amount,
                        'principal_amount' => $eachInvoiceDetail->principal_amount,
                    ];
                }
            }
        }

        return $arrInvoiceDetailFinal;
    }

    // public function getInvoicePaymentTracker($data = [])
    // {

    //     $queryBuilder = $this->tenantDB()->table('income_invoice_payment_tracker AS paymentTracker')
    //         ->select([
    //             'paymentTracker.id as payment_tracker_id',
    //             'paymentTracker.soc_id',
    //             'paymentTracker.unit_id',
    //             'paymentTracker.invoice_number',
    //             'paymentTracker.received_from',
    //             'paymentTracker.receipt_number',
    //             'paymentTracker.bill_type',
    //             'paymentTracker.payment_mode',
    //             'paymentTracker.transaction_reference',
    //             'paymentTracker.transaction_charges',
    //             'paymentTracker.late_payment_charges',
    //             'paymentTracker.writeoff_amount',
    //             'paymentTracker.payment_amount',
    //             'paymentTracker.status',
    //             'paymentTracker.payment_instrument',
    //             'paymentTracker.other_information',
    //             'paymentTracker.created_date',
    //             'paymentTracker.tds_deducted',
    //             'paymentTracker.payment_date',
    //         ])
    //         ->where('paymentTracker.soc_id', $data['soc_id']);

    //     if (!empty($data['searchData']['payment_status'])) {
    //         $queryBuilder->whereIn('paymentTracker.status', $data['searchData']['payment_status']);
    //     }

    //     if (!empty($data['searchData']['unit_id'])) {
    //         $queryBuilder->where('paymentTracker.unit_id', $data['searchData']['unit_id']);
    //     }

    //     if (!empty($data['searchData']['unit_ids'])) {
    //         $queryBuilder->whereIn('paymentTracker.unit_id', $data['searchData']['unit_ids']);
    //     }

    //     if (!empty($data['searchData']['receipt_number'])) {
    //         $queryBuilder->where('paymentTracker.receipt_number', 'LIKE', '%' . $data['searchData']['receipt_number'] . '%');
    //     }

    //     if (!empty($data['searchData']['invoice_number'])) {
    //         $queryBuilder->where('paymentTracker.invoice_number', 'LIKE', '%' . $data['searchData']['invoice_number'] . '%');
    //     }

    //     if (!empty($data['searchData']['cheque_number'])) {
    //         $queryBuilder->where('paymentTracker.transaction_reference', 'LIKE', '%' . $data['searchData']['cheque_number'] . '%');
    //     }

    //     if (!empty($data['searchData']['received_from'])) {
    //         $queryBuilder->where('paymentTracker.received_from', 'LIKE', '%' . $data['searchData']['received_from'] . '%');
    //     }

    //     if (!empty($data['searchData']['payment_date'])) {
    //         $queryBuilder->whereDate('paymentTracker.payment_date', $data['searchData']['payment_date']);
    //     }

    //     if (!empty($data['reportDateRange']['from_date']) && !empty($data['reportDateRange']['to_date'])) {
    //         $queryBuilder->whereBetween('paymentTracker.payment_date', [
    //             $data['reportDateRange']['from_date'],
    //             $data['reportDateRange']['to_date'],
    //         ]);
    //     }

    //     if (!empty($data['searchData']['bill_type'])) {
    //         $queryBuilder->whereIn('paymentTracker.bill_type', $data['searchData']['bill_type']);
    //     }

    //     if (!empty($data['filterData']['filter_status'])) {
    //         $statusMap = [
    //             'received' => 'R',
    //             'submitted' => 'P',
    //             'cleared' => 'Y',
    //             'bounced' => 'N',
    //             'not_received' => 'not_received',
    //             'reversed' => 'reversed',
    //         ];
    //         $status = $statusMap[$data['filterData']['filter_status']] ?? null;
    //         if ($status) {
    //             $queryBuilder->where('paymentTracker.status', $status);
    //         }
    //     }

    //     if (!empty($data['filterData']['filter_payment_mode'])) {
    //         $paymentModeMap = [
    //             'cheque' => 'cheque',
    //             'cash_transfer' => 'cashtransfer',
    //             'cash' => 'cash',
    //             DISPLAY_YES_BANK => DISPLAY_YES_BANK,
    //             DISPLAY_YES_BANK_ECOLLECT => DISPLAY_YES_BANK_ECOLLECT,
    //             DISPLAY_MOBIKWIK_WALLET => DISPLAY_MOBIKWIK_WALLET,
    //             DISPLAY_MOBIKWIK_PG => DISPLAY_MOBIKWIK_PG,
    //             DISPLAY_CASHFREE_PG => DISPLAY_CASHFREE_PG,
    //             DISPLAY_PAYTM => DISPLAY_PAYTM,
    //             DISPLAY_ATOM_PG => DISPLAY_ATOM_PG,
    //             DISPLAY_HDFC_PG => DISPLAY_HDFC_PG,
    //         ];
    //         $paymentMode = $paymentModeMap[$data['filterData']['filter_payment_mode']] ?? $data['filterData']['filter_payment_mode'];
    //         if ($paymentMode) {
    //             $queryBuilder->where('paymentTracker.payment_mode', $paymentMode);
    //         } elseif ($data['filterData']['filter_payment_mode'] === 'pdc') {
    //             $queryBuilder->where('paymentTracker.status', 'R')
    //                 ->where('paymentTracker.payment_date', '>=', date('Y-m-d'));
    //         }
    //     }

    //     if (!empty($data['orderBy'])) {
    //         $queryBuilder->orderBy('paymentTracker.' . $data['orderBy'], ' DESC');
    //     } else {
    //         $queryBuilder->orderBy('paymentTracker.id', 'DESC');
    //     }

    //     // if ($data['full_list'] == false || $data['full_list'] == 'false') {
    //     //     return $queryBuilder;
    //     // } else {
    //     //     return $queryBuilder->get()->toArray();
    //     // }
    //     return $queryBuilder->get()->toArray();
    // }
    public function getInvoicePaymentTracker($data = array())
    {
        $queryBuilder = $this->tenantDB()->table('income_invoice_payment_tracker AS paymentTracker')
        ->select([
            'paymentTracker.id as payment_tracker_id',
            'paymentTracker.soc_id',
            'paymentTracker.unit_id',
            'paymentTracker.invoice_number',
            'paymentTracker.received_from',
            'paymentTracker.receipt_number',
            'paymentTracker.bill_type',
            'paymentTracker.payment_mode',
            'paymentTracker.transaction_reference',
            'paymentTracker.transaction_charges',
            'paymentTracker.late_payment_charges',
            'paymentTracker.writeoff_amount',
            'paymentTracker.payment_amount',
            'paymentTracker.status',
            'paymentTracker.payment_instrument',
            'paymentTracker.other_information',
            'paymentTracker.created_date',
            'paymentTracker.tds_deducted',
            'paymentTracker.payment_date',
        ])
        ->where('paymentTracker.soc_id', $data['soc_id']);

        if (!empty($data['searchData']['payment_status'])) {
            $queryBuilder->whereIn('paymentTracker.status', $data['searchData']['payment_status']);
        }

        // check if unit_id is an array and has values then set where in condition
        if (isset($data['searchData']['unit_id']) && is_array($data['searchData']['unit_id']) && count($data['searchData']['unit_id']) > 0) {
            $queryBuilder->whereIn('paymentTracker.unit_id', $data['searchData']['unit_id']);
        } elseif (isset($data['searchData']['unit_id']) && is_numeric($data['searchData']['unit_id'])) {
            // if unit_id is a single numeric value, set where condition
            $queryBuilder->where('paymentTracker.unit_id', $data['searchData']['unit_id']);
        } else {
            // if unit_id is 0, set where condition to exclude 0
            $queryBuilder->where('paymentTracker.unit_id', '!=', 0);
        }
        // if (!empty($data['searchData']['unit_id'])) {
        //     $queryBuilder->where('paymentTracker.unit_id', $data['searchData']['unit_id']);
        // }

        if (!empty($data['searchData']['unit_ids'])) {
            $queryBuilder->whereIn('paymentTracker.unit_id', $data['searchData']['unit_ids']);
        }

        if (!empty($data['searchData']['receipt_number'])) {
            $queryBuilder->where('paymentTracker.receipt_number LIKE "%' . $data['searchData']['receipt_number'] . '%"');
        }

        if (!empty($data['searchData']['invoice_number'])) {
            $queryBuilder->where('paymentTracker.invoice_number LIKE "%' . $data['searchData']['invoice_number'] . '%"');
        }

        if (!empty($data['searchData']['cheque_number'])) {
            $queryBuilder->where('paymentTracker.transaction_reference LIKE "%' . $data['searchData']['cheque_number'] . '%"');
        }

        if (!empty($data['searchData']['received_from'])) {
            $queryBuilder->where('paymentTracker.received_from LIKE "%' . $data['searchData']['received_from'] . '%"');
        }

        if (!empty($data['searchData']['payment_date'])) {
            $queryBuilder->where(('paymentTracker.payment_date = :payment_date:'), array(
                'payment_date' => $this->getDatabaseDate($data['searchData']['payment_date'])
            ));
        }
        if (!empty($data['reportDateRange']['from_date']) && !empty($data['reportDateRange']['to_date'])) {
                    $queryBuilder->whereBetween('paymentTracker.payment_date', [
                        $data['reportDateRange']['from_date'],
                        $data['reportDateRange']['to_date'],
                    ]);
                }

                if (!empty($data['searchData']['bill_type'])) {
                            $queryBuilder->whereIn('paymentTracker.bill_type', $data['searchData']['bill_type']);
                        }

        if (!empty($data['filterData']['filter_status'])) {
            switch ($data['filterData']['filter_status']) {
                case 'received':
                    $queryBuilder->where('paymentTracker.status', 'R');
                    break;
                case 'submitted':
                    $queryBuilder->where('paymentTracker.status', 'P');
                    break;
                case 'cleared':
                    $queryBuilder->where('paymentTracker.status', 'Y');
                    break;
                case 'bounced':
                    $queryBuilder->where('paymentTracker.status', 'N');
                    break;
                case 'not_received':
                    $queryBuilder->where('paymentTracker.status', 'not_received');
                    break;
                case 'reversed':
                    $queryBuilder->where('paymentTracker.status', 'reversed');
                    break;
                default:
                        // $queryBuilder;
                        break;
            }
        }

        if (!empty($data['filterData']['filter_payment_mode'])) {
            switch ($data['filterData']['filter_payment_mode']) {
                case 'cheque':
                    $queryBuilder->where('paymentTracker.payment_mode', 'cheque');
                    break;
                case 'cash_transfer':
                    case DISPLAY_CASH_TRANSFER:
                        $queryBuilder->where('paymentTracker.payment_mode', 'cashtransfer');
                        break;
                case 'cash':
                    $queryBuilder->where('paymentTracker.payment_mode', 'cash');
                    break;
                case DISPLAY_YES_BANK:
                    $queryBuilder->where('paymentTracker.payment_mode', YES_BANK_PG);
                    break;
                case DISPLAY_YES_BANK_ECOLLECT:
                    $queryBuilder->where('paymentTracker.payment_mode', YES_BANK_ECOLLECT);
                    break;
                case DISPLAY_MOBIKWIK_WALLET:
                    $queryBuilder->where('paymentTracker.payment_mode', MOBIKWIK_WALLET);
                    break;
                case DISPLAY_MOBIKWIK_PG:
                    $queryBuilder->where('paymentTracker.payment_mode', MOBIKWIK_PG);
                    break;
                case DISPLAY_CASHFREE_PG:
                    $queryBuilder->where('paymentTracker.payment_mode', CASHFREE_PG);
                    break;
                case DISPLAY_PAYTM:
                    $queryBuilder->where('paymentTracker.payment_mode', PAYTM_PG);
                    break;
                case DISPLAY_ATOM_PG:
                    $queryBuilder->where('paymentTracker.payment_mode', ATOM_PG);
                    break;
                case DISPLAY_HDFC_PG:
                    $queryBuilder->where('paymentTracker.payment_mode', HDFC_PG);
                    break;
                case 'pdc':
                    $queryBuilder->where('paymentTracker.status', 'R')
                    ->where('paymentTracker.payment_date', '>=', $this->getCurrentDate('database'));
                    break;
                default:
                $queryBuilder->where('paymentTracker.payment_mode', $data['filterData']['filter_payment_mode']);
                break;
            }
        }

        if (!empty($data['orderBy'])) {
            $queryBuilder->orderBy('paymentTracker.' . $data['orderBy'], ' DESC');
        } else {
            $queryBuilder->orderBy('paymentTracker.id', 'DESC');
        }

        return $queryBuilder->get()->toArray();
    }


    public function getMaintenanceInvoiceDetailByDateRange($data)
    {
        $query = $this->tenantDB()->table('income_unit_invoices as I')
            ->select([
                'I.fk_unit_id',
                'I.soc_building_name',
                'I.unit_name',
                'I.bill_to',
                'I.invoice_number',
                DB::raw('SUM(P.amount) as invoice_amount'),
                'I.principal_amount',
                'I.interest_amount',
                'I.advance_amount',
                'I.outstanding_principal',
                'I.outstanding_interest',
                'I.roundoff_amount',
                'I.from_date',
                'I.to_date',
                DB::raw('DATE_FORMAT(I.created_date, "%Y-%m-%d") AS created_dt'),
            ])
            ->leftJoin('income_invoice_particular as P', 'I.invoice_number', '=', 'P.invoice_number')
            ->where('I.soc_id', $data['soc_id'])
            ->whereBetween('I.created_date', [$data['from_date'], $data['to_date']])
            ->where('I.status', '!=', 'cancelled');

        // check if unit_id is an array and has values then set where in condition
        if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
            $query->whereIn('I.fk_unit_id', $data['unit_id']);
        } elseif (isset($data['unit_id']) && is_numeric($data['unit_id'])) {
            // if unit_id is a single numeric value, set where condition
            $query->where('I.fk_unit_id', $data['unit_id']);
        } else {
            // if unit_id is 0, set where condition to exclude 0
            $query->where('I.fk_unit_id', '!=', 0);
        }
        // if ($data['unit_id']) {
        //     $query->where('I.fk_unit_id', $data['unit_id']);
        // } else {
        //     $query->where('I.fk_unit_id', '!=', 0);
        // }

        $query->groupBy('I.invoice_number');

        return $query->get()->toArray();
    }

    public function getUnitCreditRefunds($data = [])
    {
        // Initialize an empty array for credit details
        $arrCreditDetails = [];

        // Check if both 'soc_id' and 'unit_id' are provided
        if (!empty($data['soc_id']) && !empty($data['unit_id'])) {
            // Query CreditAccounts using Eloquent
            $arrCreditDetails = $this->tenantDB()->table('chsone_credit_accounts')->where('soc_id', $data['soc_id'])
            // ->where('account_id', $data['unit_id'])
            ->where('transaction_type', 'dr')
            ->where('use_credit', 'refundable');
        
            // check if unit_id is an array and has values then set where in condition
            if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                $arrCreditDetails->whereIn('account_id', $data['unit_id']);
            } else {
                $arrCreditDetails->where('account_id', $data['unit_id']);
            }

            $arrCreditDetails = $arrCreditDetails->get()->toArray();
        }

        return $arrCreditDetails;
    }

    public function getIncidentInvoiceDetailByDateRange($data = [])
    {
        $arrMemberInvoiceDetail = [];

        if (!empty($data['soc_id'])) {
            $query = $this->tenantDB()->table('income_common_billing_charges as I')
                ->selectRaw('
                    I.id,
                    concat(m.member_first_name, " ", m.member_last_name) as member_name,
                    I.fk_unit_id,
                    I.invoice_number,
                    I.amount,
                    I.advance_amount,
                    SUM(T.tax_amount) as tax_amount,
                    I.from_date,
                    I.to_date,
                    DATE_FORMAT(I.bill_date, "%Y-%m-%d") AS created_dt
                ')
                ->leftJoin('chsone_members_master as m', 'm.fk_unit_id', '=', 'I.fk_unit_id')
                ->leftJoin('chsone_tax_log as T', 'I.invoice_number', '=', 'T.invoice_number')
                ->where('I.soc_id', $data['soc_id'])
                ->whereBetween('I.bill_date', [
                    $data['from_date'],
                    $data['to_date'],
                ])
                ->where('I.status', '!=', 'cancelled')
                ->where('m.approved', 1)
                ->where('m.status', 1)
                ->where('m.member_type_id', 1)
                ->where('I.payment_status', '!=', 'cancelled')
                ->groupBy('I.invoice_number');
            
            // check if unit_id is an array and has values then set where in condition
            if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                $query->whereIn('I.fk_unit_id', $data['unit_id']);
            } elseif (isset($data['unit_id']) && is_numeric($data['unit_id'])) {
                // if unit_id is a single numeric value, set where condition
                $query->where('I.fk_unit_id', $data['unit_id']);
            } else {
                // if unit_id is 0, set where condition to exclude 0
                $query->where('I.fk_unit_id', '!=', 0);
            }
            // if (!empty($data['unit_id'])) {
            //     $query->where('I.fk_unit_id', $data['unit_id']);
            // } else {
            //     $query->where('I.fk_unit_id', '!=', 0);
            // }

            $arrMemberInvoiceDetail = $query->get()->toArray();
        }

        return $arrMemberInvoiceDetail;
    }


    public function getIncidentInvoiceInterestDetailByDateRange($data = [])
    {
        $arrMemberInvoiceDetail = [];

        if (!empty($data['soc_id'])) {
            $query = $this->tenantDB()->table('income_common_billing_charges as I')
                ->select([
                    'I.id',
                    DB::raw("CONCAT(m.member_first_name, ' ', m.member_last_name) as member_name"),
                    'I.fk_unit_id',
                    'I.invoice_number',
                    'I.amount',
                    'I.advance_amount',
                    'U.interest_amount',
                    'I.from_date',
                    'I.to_date',
                    DB::raw("DATE_FORMAT(I.bill_date, '%Y-%m-%d') AS created_dt"),
                ])
                ->leftJoin('chsone_members_master as m', 'm.fk_unit_id', '=', 'I.fk_unit_id')
                ->leftJoin('income_unit_invoices as U', 'I.invoice_number', '=', 'U.invoice_number')
                ->where('I.soc_id', $data['soc_id'])
                ->whereBetween('I.bill_date', [
                    $data['from_date'],
                    $data['to_date'],
                ])
                ->where('I.status', '!=', 'cancelled')
                ->where('I.payment_status', '!=', 'cancelled')
                ->where('m.approved', 1)
                ->where('m.status', 1)
                ->where('m.member_type_id', 1)
                ->groupBy('I.invoice_number');

            // check if unit_id is an array and has values then set where in condition
            if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                $query->whereIn('I.fk_unit_id', $data['unit_id']);
            } elseif (isset($data['unit_id']) && is_numeric($data['unit_id'])) {
                // if unit_id is a single numeric value, set where condition
                $query->where('I.fk_unit_id', $data['unit_id']);
            } else {
                // if unit_id is 0, set where condition to exclude 0
                $query->where('I.fk_unit_id', '!=', 0);
            }
            // if (!empty($data['unit_id'])) {
            //     $query->where('I.fk_unit_id', $data['unit_id']);
            // } else {
            //     $query->where('I.fk_unit_id', '!=', 0);
            // }

            $arrMemberInvoiceDetail = $query->get()->toArray();
        }

        return $arrMemberInvoiceDetail;
    }



    public function getRoundOffIncidentInvoice($data = [])
    {
        $arrMemberInvoiceDetail = [];

        if (!empty($data['soc_id'])) {
            $query = $this->tenantDB()->table('income_common_billing_charges as I')
                ->select([
                    'I.id',
                    DB::raw("CONCAT(m.member_first_name, ' ', m.member_last_name) as member_name"),
                    'I.fk_unit_id',
                    'I.invoice_number',
                    'I.amount',
                    'I.advance_amount',
                    'L.transaction_amount',
                    'I.from_date',
                    'I.to_date',
                    DB::raw("DATE_FORMAT(I.bill_date, '%Y-%m-%d') AS created_dt"),
                ])
                ->leftJoin('chsone_members_master as m', 'm.fk_unit_id', '=', 'I.fk_unit_id')
                ->leftJoin('chsone_ledger_transactions as L', 'I.invoice_number', '=', 'L.voucher_reference_number')
                ->where('I.soc_id', $data['soc_id'])
                ->whereBetween('I.bill_date', [
                    $data['from_date'],
                    $data['to_date'],
                ])
                ->where('I.status', '!=', 'cancelled')
                ->where('I.payment_status', '!=', 'cancelled')
                ->where('m.approved', 1)
                ->where('m.status', 1)
                ->where('m.member_type_id', 1)
                ->where('L.voucher_reference_number', $data['invoice_number'])
                ->where('L.ledger_account_name', 'Income Round Off')
                ->groupBy('I.invoice_number');
            
            // check if unit_id is an array and has values then set where in condition
            if (isset($data['fk_unit_id']) && is_array($data['fk_unit_id']) && count($data['fk_unit_id']) > 0) {
                $query->whereIn('I.fk_unit_id', $data['fk_unit_id']);
            } elseif (isset($data['fk_unit_id']) && is_numeric($data['fk_unit_id'])) {
                // if unit_id is a single numeric value, set where condition
                $query->where('I.fk_unit_id', $data['fk_unit_id']);
            } else {
                // if unit_id is 0, set where condition to exclude 0
                $query->where('I.fk_unit_id', '!=', 0);
            }
            // if (!empty($data['fk_unit_id'])) {
            //     $query->where('I.fk_unit_id', $data['fk_unit_id']);
            // } else {
            //     $query->where('I.fk_unit_id', '!=', 0);
            // }

            $arrMemberInvoiceDetail = $query->get()->toArray();
        }

        return $arrMemberInvoiceDetail;
    }


    // public function getVoucherDetails($data)
    // {
    //     // Initialize as a Laravel collection
    //     $arrVoucherDetail = array();

    //     // Check if the necessary parameters are provided
    //     if (!empty($data['soc_id']) && !empty($data['unit_id']) && !empty($data['from_date']) && !empty($data['to_date'])) {
    //         // Ensure dates are formatted correctly
    //         $fromDate = $data['from_date'];
    //         $toDate = $data['to_date'];

    //         // First part of the query
    //         $query1 = $this->tenantDB()->table('chsone_voucher_master as V')
    //             ->leftJoin('chsone_units_master as U', function ($join) {
    //                 $join->on('U.ledger_account_id', '=', 'V.from_ledger_account_id')
    //                     ->orOn('U.ledger_account_id', '=', 'V.to_ledger_account_id');
    //             })
    //             ->leftJoin('chsone_members_master as M', 'M.fk_unit_id', '=', 'U.unit_id')
    //             ->selectRaw("CONCAT(M.member_first_name, ' ', M.member_last_name) AS member_name")
    //             ->addSelect([
    //                 'V.voucher_id AS id',
    //                 'V.type',
    //                 'V.transaction_date',
    //                 'V.amount',
    //                 'U.ledger_account_id',
    //                 DB::raw("IF(U.ledger_account_id = V.from_ledger_account_id, 'dr', 'cr') AS nature"),
    //                 'V.from_ledger_account_id',
    //                 'V.from_ledger_account_name',
    //                 'V.to_ledger_account_id',
    //                 'V.to_ledger_account_name',
    //                 'V.reference',
    //             ])
    //             ->where([
    //                 ['M.approved', '=', 1],
    //                 ['M.status', '=', 1],
    //                 ['M.member_type_id', '=', 1],
    //                 ['U.unit_id', '=', $data['unit_id']],
    //                 ['U.soc_id', '=', $data['soc_id']],
    //                 ['V.status', '=', 1],
    //             ])
    //             ->whereBetween('V.transaction_date', [$fromDate, $toDate]);

    //         // Second part of the query
    //         $query2 = $this->tenantDB()->table('chsone_voucher_master as V')
    //             ->leftJoin('chsone_grp_ledger_tree as L', function ($join) {
    //                 $join->on('L.ledger_account_id', '=', 'V.from_ledger_account_id')
    //                     ->orOn('L.ledger_account_id', '=', 'V.to_ledger_account_id');
    //             })
    //             ->selectRaw("'' AS member_name")
    //             ->addSelect([
    //                 'V.voucher_id AS id',
    //                 'V.type',
    //                 'V.transaction_date',
    //                 'V.amount',
    //                 'L.ledger_account_id',
    //                 DB::raw("IF(L.ledger_account_id = V.from_ledger_account_id, 'dr', 'cr') AS nature"),
    //                 'V.from_ledger_account_id',
    //                 'V.from_ledger_account_name',
    //                 'V.to_ledger_account_id',
    //                 'V.to_ledger_account_name',
    //                 'V.reference',
    //             ])
    //             ->whereRaw('L.ledger_account_id = (
    //                 SELECT ledger_account_id
    //                 FROM chsone_grp_ledger_tree
    //                 WHERE ledger_account_name = (
    //                     SELECT LOWER(SUBSTR(ledger_account_name, LOCATE("#", ledger_account_name) + 1))
    //                     FROM chsone_grp_ledger_tree
    //                     WHERE ledger_account_id = (
    //                         SELECT ledger_account_id
    //                         FROM chsone_units_master
    //                         WHERE unit_id = ?
    //                     )
    //                 )
    //             )', [$data['unit_id']])
    //             ->where('V.status', '=', 1)
    //             ->whereBetween('V.transaction_date', [$fromDate, $toDate]);

    //         // Combine results using union and execute the query
    //         $arrVoucherDetail = $query1->union($query2)->get();
    //     }

    //     return $arrVoucherDetail;
    // }

    public function getVoucherDetails($data)
    {
        // Initialize result array
        $arrVoucherDetail = [];

        // Check if the required data is provided
        if (!empty($data['soc_id']) && !empty($data['unit_id']) && !empty($data['from_date']) && !empty($data['to_date'])) {
            $fromDate = $data['from_date'];
            $toDate = $data['to_date'];
            $unitIds = is_array($data['unit_id']) ? $data['unit_id'] : [$data['unit_id']];

            // First query
            $query1 = $this->tenantDB()->table('chsone_voucher_master as V')
                ->leftJoin('chsone_units_master as U', function ($join) {
                    $join->on('U.ledger_account_id', '=', 'V.from_ledger_account_id')
                        ->orOn('U.ledger_account_id', '=', 'V.to_ledger_account_id');
                })
                ->leftJoin('chsone_members_master as M', 'M.fk_unit_id', '=', 'U.unit_id')
                ->selectRaw("CONCAT(M.member_first_name, ' ', M.member_last_name) AS member_name")
                ->addSelect([
                    'V.voucher_id AS id',
                    'V.type',
                    'V.transaction_date',
                    'V.amount',
                    'U.ledger_account_id',
                    DB::raw("IF(U.ledger_account_id = V.from_ledger_account_id, 'dr', 'cr') AS nature"),
                    'V.from_ledger_account_id',
                    'V.from_ledger_account_name',
                    'V.to_ledger_account_id',
                    'V.to_ledger_account_name',
                    'V.reference',
                ])
                ->where([
                    ['M.approved', '=', 1],
                    ['M.status', '=', 1],
                    ['M.member_type_id', '=', 1],
                    ['U.soc_id', '=', $data['soc_id']],
                    ['V.status', '=', 1],
                ])
                ->whereIn('U.unit_id', $unitIds)
                ->whereBetween('V.transaction_date', [$fromDate, $toDate]);

            // Second query
            $query2 = $this->tenantDB()->table('chsone_voucher_master as V')
                ->leftJoin('chsone_grp_ledger_tree as L', function ($join) {
                    $join->on('L.ledger_account_id', '=', 'V.from_ledger_account_id')
                        ->orOn('L.ledger_account_id', '=', 'V.to_ledger_account_id');
                })
                ->selectRaw("'' AS member_name")
                ->addSelect([
                    'V.voucher_id AS id',
                    'V.type',
                    'V.transaction_date',
                    'V.amount',
                    'L.ledger_account_id',
                    DB::raw("IF(L.ledger_account_id = V.from_ledger_account_id, 'dr', 'cr') AS nature"),
                    'V.from_ledger_account_id',
                    'V.from_ledger_account_name',
                    'V.to_ledger_account_id',
                    'V.to_ledger_account_name',
                    'V.reference',
                ])
                ->where(function ($query) use ($unitIds) {
                    foreach ($unitIds as $unitId) {
                        $query->orWhereRaw('L.ledger_account_id = (
                            SELECT ledger_account_id
                            FROM chsone_grp_ledger_tree
                            WHERE ledger_account_name = (
                                SELECT LOWER(SUBSTR(ledger_account_name, LOCATE("#", ledger_account_name) + 1))
                                FROM chsone_grp_ledger_tree
                                WHERE ledger_account_id = (
                                    SELECT ledger_account_id
                                    FROM chsone_units_master
                                    WHERE unit_id = ?
                                )
                            )
                        )', [$unitId]);
                    }
                })
                ->where('V.status', '=', 1)
                ->whereBetween('V.transaction_date', [$fromDate, $toDate]);

            // Combine and get results
            $arrVoucherDetail = $query1->union($query2)->get();
        }

        return $arrVoucherDetail;
    }

    // Helper function to ensure the date format is correct
    protected function formatDate($date)
    {
        return date('Y-m-d', strtotime($date)); // Convert to YYYY-MM-DD format
    }

    // public function getCreditNoteDetails($data)
    // {
    //     $arrCreditNoteDetail = collect(); // Initialize as a Laravel collection

    //     if (!empty($data['soc_id']) && !empty($data['unit_id']) && !empty($data['from_date']) && !empty($data['to_date'])) {
    //         // First query
    //         $query1 = $this->tenantDB()->table('chsone_credit_note as cn')
    //             ->leftJoin('chsone_credit_accounts as ca', 'cn.credit_id', '=', 'ca.credit_account_id')
    //             ->select([
    //                 'cn.credit_id',
    //                 'cn.credit_type',
    //                 'cn.payment_date',
    //                 'cn.amount',
    //                 'ca.transaction_type',
    //                 'ca.account_name',
    //             ])
    //             ->where('cn.account_id', $data['unit_id'])
    //             ->where('ca.account_context', 'unit')
    //             ->where('cn.soc_id', $data['soc_id'])
    //             ->whereBetween('cn.payment_date', [$data['from_date'], $data['to_date']]);

    //         // Execute the first query
    //         $arrCreditNoteDetail = $query1->get();

    //         // If no results from the first query, perform the second query
    //         if ($arrCreditNoteDetail->isEmpty()) {
    //             $query2 = $this->tenantDB()->table('chsone_credit_note as cn')
    //                 ->leftJoin('chsone_ledger_transactions as txn', 'cn.credit_id', '=', 'txn.voucher_reference_id')
    //                 ->leftJoin('chsone_members_master as M', 'M.fk_unit_id', '=', 'cn.account_id')
    //                 ->select([
    //                     'cn.credit_id',
    //                     'cn.credit_type',
    //                     DB::raw('txn.transaction_date as payment_date'),
    //                     'cn.amount',
    //                     DB::raw("concat(M.member_first_name, ' ', M.member_last_name) as account_name"),
    //                 ])
    //                 ->where([
    //                     ['M.approved', '=', 1],
    //                     ['M.status', '=', 1],
    //                     ['M.member_type_id', '=', 1],
    //                     ['cn.amount', '=', DB::raw('txn.transaction_amount')],
    //                     ['cn.account_id', '=', $data['unit_id']],
    //                     ['txn.transaction_type', '=', 'cr'],
    //                     ['txn.voucher_type', '=', 'credit note'],
    //                     ['cn.soc_id', '=', $data['soc_id']],
    //                 ])
    //                 ->whereBetween('txn.transaction_date', [$data['from_date'], $data['to_date']]);

    //             // Execute the second query
    //             $arrCreditNoteDetail = $query2->get();
    //         }
    //     }

    //     return $arrCreditNoteDetail;
    // }

    public function getCreditNoteDetails($data)
    {
        $arrCreditNoteDetail = collect(); // Initialize as a Laravel collection

        if (!empty($data['soc_id']) && !empty($data['unit_id']) && !empty($data['from_date']) && !empty($data['to_date'])) {
            $fromDate = $data['from_date'];
            $toDate = $data['to_date'];

            // Normalize unit_id to array
            $unitIds = is_array($data['unit_id']) ? $data['unit_id'] : [$data['unit_id']];

            // First query
            $query1 = $this->tenantDB()->table('chsone_credit_note as cn')
                ->leftJoin('chsone_credit_accounts as ca', 'cn.credit_id', '=', 'ca.credit_account_id')
                ->select([
                    'cn.credit_id',
                    'cn.credit_type',
                    'cn.payment_date',
                    'cn.amount',
                    'ca.transaction_type',
                    'ca.account_name',
                ])
                ->whereIn('cn.account_id', $unitIds)
                ->where('ca.account_context', 'unit')
                ->where('cn.soc_id', $data['soc_id'])
                ->whereBetween('cn.payment_date', [$fromDate, $toDate]);

            // Execute the first query
            $arrCreditNoteDetail = $query1->get();

            // If no results from the first query, perform the second query
            if ($arrCreditNoteDetail->isEmpty()) {
                $query2 = $this->tenantDB()->table('chsone_credit_note as cn')
                    ->leftJoin('chsone_ledger_transactions as txn', 'cn.credit_id', '=', 'txn.voucher_reference_id')
                    ->leftJoin('chsone_members_master as M', 'M.fk_unit_id', '=', 'cn.account_id')
                    ->select([
                        'cn.credit_id',
                        'cn.credit_type',
                        DB::raw('txn.transaction_date as payment_date'),
                        'cn.amount',
                        DB::raw("concat(M.member_first_name, ' ', M.member_last_name) as account_name"),
                    ])
                    ->where([
                        ['M.approved', '=', 1],
                        ['M.status', '=', 1],
                        ['M.member_type_id', '=', 1],
                        ['cn.amount', '=', DB::raw('txn.transaction_amount')],
                        ['txn.transaction_type', '=', 'cr'],
                        ['txn.voucher_type', '=', 'credit note'],
                        ['cn.soc_id', '=', $data['soc_id']],
                    ])
                    ->whereIn('cn.account_id', $unitIds)
                    ->whereBetween('txn.transaction_date', [$fromDate, $toDate]);

                // Execute the second query
                $arrCreditNoteDetail = $query2->get();
            }
        }

        return $arrCreditNoteDetail;
    }

    public function formatUnitStatementDetailReport($data = [])
    {
        $arrUnitStatementSummary = ['debit' => 0, 'credit' => 0, 'total' => 0];
        $arrUnitStatementDetail = [];
        $balance = 0;
        $result = [];
        if (!empty($data['arrUnitStatementDetail'])) {
            $i = 0;
            foreach ($data['arrUnitStatementDetail'] as $eachUnitStatement) {
                $arrUnitStatementDetail[$i]['date'] = $this->formatDate($eachUnitStatement['date']);
                $arrUnitStatementDetail[$i]['type'] = ucfirst($eachUnitStatement['type']);
                $arrUnitStatementDetail[$i]['reference_id'] = !empty($eachUnitStatement['reference_id']) ? ($eachUnitStatement['reference_id'] . '(' . current(explode(' ', $eachUnitStatement["display_type"])) . ')') : $eachUnitStatement["display_type"];
                $arrUnitStatementDetail[$i]['payment_reference'] = !empty(trim($eachUnitStatement['payment_reference'])) ? ucfirst($eachUnitStatement['payment_reference']) : 'NA';
                $arrUnitStatementDetail[$i]['debit'] = (!empty($eachUnitStatement['type']) && strtolower($eachUnitStatement['type']) == 'invoice') ? $eachUnitStatement['amount'] : 0;
                $arrUnitStatementDetail[$i]['credit'] = (!empty($eachUnitStatement['type']) && strtolower($eachUnitStatement['type']) != 'invoice') ? $eachUnitStatement['amount'] : 0;

                if (!empty($eachUnitStatement['nature'])) {
                    switch ($eachUnitStatement['nature']) {
                        case 'dr':
                            $arrUnitStatementDetail[$i]['credit'] = 0;
                            $arrUnitStatementDetail[$i]['debit'] = $eachUnitStatement['amount'];
                            break;
                        case 'cr':
                            $arrUnitStatementDetail[$i]['debit'] = 0;
                            $arrUnitStatementDetail[$i]['credit'] = $eachUnitStatement['amount'];
                            break;
                    }
                }

                // $balance = ($i == 0)
                // ? $arrUnitStatementDetail[$i]['debit'] - $arrUnitStatementDetail[$i]['credit']
                // : $balance + $arrUnitStatementDetail[$i]['debit'] - $arrUnitStatementDetail[$i]['credit'];

                if ($i == 0) {
                    $balance = $arrUnitStatementDetail[$i]['debit'] - $arrUnitStatementDetail[$i]['credit'];
                } else {
                    $balance = $balance + $arrUnitStatementDetail[$i]['debit'] - $arrUnitStatementDetail[$i]['credit'];
                }
                $arrUnitStatementDetail[$i]['balance'] = $balance;
                $arrUnitStatementSummary['debit'] += $arrUnitStatementDetail[$i]['debit'];
                $arrUnitStatementSummary['credit'] += $arrUnitStatementDetail[$i]['credit'];
                $i++;
            }

            $arrUnitStatementSummary['total'] = round($arrUnitStatementSummary['debit'] - $arrUnitStatementSummary['credit'], 3);
            $arrUnitStatementSummary['debit_sum'] = $arrUnitStatementSummary['debit'];
            $arrUnitStatementSummary['credit_sum'] = $arrUnitStatementSummary['credit'];
            $arrUnitStatementSummary['total_sum'] = $arrUnitStatementSummary['total'];
            $arrUnitStatementSummary['id'] = 1;

         
            $result['id'] = 1;
            $result['debit_sum'] = $arrUnitStatementSummary['debit_sum'];
            $result['credit_sum'] = $arrUnitStatementSummary['credit_sum'];
            $result['total_sum']   = $result['debit_sum'] - $result['credit_sum'];

        }

        // $idCounter = 1;

        // Loop through the array to assign IDs
        // foreach ($arrUnitStatementDetail as $key => $value) {
        //     // Assign the counter value as the 'id'
        //     $arrUnitStatementDetail[$key]['id'] = $idCounter++;
        // }

        // set the count of the $arrUnitStatementDetail
        $count = count($arrUnitStatementDetail);
        $this->meta['pagination']['total'] = $count;

        // set the pagination data
        $page = isset($this->input['page']) ? $this->input['page'] : 1;
        $per_page = !empty($this->input['per_page']) ? $this->input['per_page'] : ($this->hugeData ? 100000 : 10);
        $offset = ($page - 1) * $per_page;

        // need to set the pagination only to the $arrUnitStatementDetail
        $arrUnitStatementDetail = array_slice($arrUnitStatementDetail, $offset, $per_page);

        return [
            $arrUnitStatementDetail,
            [$result],
        ];

        // return [
        //     $arrUnitStatementDetail,
        //     [$arrUnitStatementSummary],
        // ];
    }


    public function getUnitCreditByTrackerId($data = [])
    {

        $arrCreditDetails = [];

        // Ensure that required fields are present
        if (!empty($data['soc_id']) && !empty($data['payment_tracker_id']) && !empty($data['unit_id'])) {
            // Fetch credit details based on the given conditions
            $arrCreditDetails = $this->tenantDB()->table('chsone_credit_accounts')
                ->where('soc_id', $data['soc_id'])
                // ->where('account_id', $data['unit_id'])
                ->whereIn('payment_tracker_id', $data['payment_tracker_id']);

                // check if unit_id is an array and has values then set where in condition
                if (isset($data['unit_id']) && is_array($data['unit_id']) && count($data['unit_id']) > 0) {
                    $arrCreditDetails->whereIn('account_id', $data['unit_id']);
                } else {
                    $arrCreditDetails->where('account_id', $data['unit_id']);
                }

                $arrCreditDetails = $arrCreditDetails->get();

            // Convert the result to an array if it's not empty
            $arrCreditDetails = !$arrCreditDetails->isEmpty() ? $arrCreditDetails->toArray() : [];
        }

        return $arrCreditDetails;
    }
}
