<?php

namespace App\Console\Commands\Workflows\Income;

use App\Console\Commands\Workflow;

class DownloadMembersUnitStatementReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMembersUnitStatementReport {input}';

    protected $description = 'Download Members Unit Statement Report in Excel or PDF';

    protected $rules = [];

    protected $rulesMessage = [];

    protected $headings = [
        'Date',
        'Type',
        'Reference ID',
        'Payment Reference',
        'Debit',
        'Credit',
        'Balance'
    ];

    protected $formatter = [
        'date' => '',
        'type' => '',
        'reference_id' => '',
        'payment_reference' => '',
        'debit' => '',
        'credit' => '',
        'balance' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $membersUnitStatement = $this->action('datasource:membersUnitStatementReport', $this->pointer, $this->request);
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($membersUnitStatement, $this->headings, 'members_unit_statement_');
                $this->data['url'] = $data['data'];
            }
            else{
                
                $data = $this->hitCURLForGeneratePDF($membersUnitStatement, $this->headings, 'member_unit_ledger');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
