<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class NonMembersReceivableReportDownloadWorkflow extends Workflow
{
    protected $signature = 'workflow:nonMembersReceivableReportDownload {input}';

    protected $description = 'Download Non-Members Receivable Report Workflow';

    protected $rules = [
    ];

    protected $rulesMessage = [
    ];

    protected $formatter = [
        'full_name' => '',
        'due_amount' => '',
        'cr_bal' => '',
        'ledger_bal' => ''
    ];

    protected $formatterByKeys = [
        'id'
    ];

    protected $headings = [
        'Name',
        'Payment Dues',
        'Credit Balance',
        'Ledger Balance'
    ];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else
        {
            $nonMembersReceivableData = $this->action('datasource:nonMembersReceivableReport', $this->pointer, $this->request);
            
            // Debug: Check the actual data structure
            // dd($nonMembersReceivableData);
            
            // Handle the nested array structure - data comes as [[detail_rows], [summary_totals]]
            $detailRows = $nonMembersReceivableData[0] ?? [];
            $summaryTotals = $nonMembersReceivableData[1][0] ?? [];
            
            // Format data to match screenshot headers
            $outputData = [];
            foreach ($detailRows as $item) {
                $outputData[] = [
                    'Name' => $item['full_name'] ?? '',
                    'Payment Dues' => $item['due_amount'] ?? 0,
                    'Credit Balance' => $item['cr_bal'] ?? 0,
                    'Ledger Balance' => $item['ledger_bal'] ?? 0,
                ];
            }
            
            // Add Total row from provided totals
            $totalRow = [
                'Name' => 'Total',
                'Payment Dues' => $summaryTotals['total_payment_dues'] ?? '',
                'Credit Balance' => $summaryTotals['total_credit_balance'] ?? '',
                'Ledger Balance' => $summaryTotals['total_ledger_balance'] ?? '',
            ];
            $outputData[] = $totalRow;
            
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($outputData, $this->headings, 'nonMembersReceivableReport_');
                $this->data['url'] = $data['data'];
            }
            else{
                $data = $this->hitCURLForGeneratePDF($outputData, $this->headings, 'nonMembersReceivableReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
