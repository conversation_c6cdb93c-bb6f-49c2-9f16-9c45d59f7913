<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class DownloadMembersInvoiceDetailReportWorkflow extends Workflow
{
    protected $signature = 'workflow:downloadMembersInvoiceDetailReport {input}';

    protected $description = 'Download Members Invoice Detail Report in Excel or PDF';

    protected $rules = [];

    protected $rulesMessage = [];

    protected $headings = [
        'Unit Name',
        'Bill To',
        'GSTIN',
        'Invoice Number',
        'Maintenance Fee',
        'Period',
        'Sinking Fund',
        'Interest',
        'Tax',
        'Invoice Amount',
        'P Arrears',
        'I Arrears',
        'Payable',
        'Receipt',
        'Net Due',
        'Advance Credit'
    ];

    protected $formatter = [
        'unit_name' => '',
        'bill_to' => '',
        'gstin' => '',
        'invoice_number' => '',
        'maintenance_fee' => '',
        'period' => '',
        'sinking_fund' => '',
        'interest' => '',
        'tax' => '',
        'invoice_amount' => '',
        'p_arrears' => '',
        'i_arrears' => '',
        'payable' => '',
        'receipt' => '',
        'net_due' => '',
        'advance_credit' => ''
    ];

    protected $formatterByKeys = [];

    protected $mapper = [];

    public function apply()
    {
        $type = $this->input['type'];

        if(!$type || $type == '' || $type == ':type')
        {
            $this->status = 'error';
            $this->message = 'Please provide a valid type either excel or pdf';
            $this->statusCode = 400;
            $this->data = [];
        }
        else{
            $membersInvoiceDetail = $this->action('datasource:membersInvoiceDetailReport', $this->pointer, $this->request);
            $this->data = [];

            if($type == 'excel')
            {
                $data = $this->hitCURLForGenerateCSV($membersInvoiceDetail, $this->headings, 'members_invoice_detail_');
                $this->data['url'] = $data['data'];
            }
            else{
                
                $data = $this->hitCURLForGeneratePDF($membersInvoiceDetail, $this->headings, 'membersInvoiceDetailReport');
                $this->data['url'] = $data['data'];
            }
        }
    }
}
