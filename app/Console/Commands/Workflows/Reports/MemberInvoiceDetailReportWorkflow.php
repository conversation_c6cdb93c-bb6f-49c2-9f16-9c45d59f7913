<?php

namespace App\Console\Commands\Workflows\Reports;

use Illuminate\Console\Command;
use App\Console\Commands\Workflow;

class MemberInvoiceDetailReportWorkflow extends Workflow
{
    protected $signature = 'workflow:memberInvoiceDetailReport {input}';
    protected $description = 'Member Invoice Detail Report Workflow';

    public function apply()
    {
        $rules = [
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ];
        $messages = [
            'from_date.required' => 'From date is required',
            'to_date.required' => 'To date is required',
            'to_date.after_or_equal' => 'To date must be after or equal to from date',
        ];
        $memberInvoiceReport = $this->action('datasource:membersInvoiceDetailReport', $this->pointer, $this->request);
        $this->data = $memberInvoiceReport;
        //$this->data = $this->format((array)$this->data);
        $this->meta['schema'] = [
            "table" => [
                "tableTitle" => [
                    "Members Invoice Detail Report",
                    "Summary"
                ],
                "extraFilters" => [
                    "from_date" => [
                        "title" => "From Date",
                        "type" => "date"
                    ],
                    "order" => [
                        "title" => "Order",
                        "options" => [
                           "asc" => "Ascending",
                           "desc" => "Descending"
                        ],
                        "type" => "select",
                        'select_single' => true,
                        "defaultValue" => "asc"
                   ],
                   "sort_by" => [
                        "title" => "Sort By",
                        "options" => [
                            "unit_id" => "Unit ID",
                            "unit_name" => "Unit Name"
                        ],
                        "type" => "select",
                        'select_single' => true,
                        "defaultValue" => "unit_id"
                   ],
                ],
                'actions' => [
                    [
                        "title" => "Print/Export",
                        "icon" => "ri-printer-line",
                        "options" => [
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "url" => "/admin/income-details/membersInvoiceDetailReport/download/pdf",
                                    "method" => "GET",
                                    "type" => "download"
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "url" => "/admin/income-details/membersInvoiceDetailReport/download/excel",
                                    "method" => "GET",
                                    "type" => "download"
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "*"
                ],
                "columns" => [
                    [
                        [
                            "title" => "Sr No.",
                            "key" => "sr_no"
                        ],
                        [
                            "title" => "Unit Name",
                            "key" => "unit_name"
                        ],
                        [
                            "title" => "Bill To",
                            "key" => "bill_to"
                        ],
                        [
                            "title" => "GSTIN",
                            "key" => "gstin"
                        ],
                        [
                            "title" => "ITS No.",
                            "key" => "its_no"
                        ],
                        [
                            "title" => "Invoice No.",
                            "key" => "invoice_number"
                        ],
                        [
                            "title" => 'Maintenance Fee',
                            'key' => 'maintenance_fee'
                        ],
                        [
                            'title' => 'Sinking Fund',
                            'key' => 'sinking_fund'
                        ],
                        [
                            'title' => 'Interest',
                            'key' => 'interest'
                        ],
                        [
                            'title' => 'Tax',
                            'key' => 'tax'
                        ],
                        [
                            'title' => 'Invoice Amount',
                            'key' => 'invoice_amount'
                        ],
                        [
                            'title' => 'Principal Arrears',
                            'key' => 'p_arrears'
                        ],
                        [
                            'title' => 'Interest Arrears',
                            'key' => 'i_arrears'
                        ],
                        [
                            "title" => "Credit/Adjustment",
                            "key" => "credit_adjustment",
                            "value" => 0,
                            "type" => "amount"
                        ],
                        [
                            'title' => 'Payable',
                            'key' => 'payable'
                        ],
                        [
                            'title' => 'Receipt',
                            'key' => 'receipt'
                        ],
                        [
                            'title' => 'Net Due',
                            'key' => 'net_due'
                        ],
                        [
                            'title' => 'Advance Credit',
                            'key' => 'advance_credit'
                        ],
                        // [
                        //     'title' => '',
                        //     // Add any additional columns if needed
                        //     'key' => 'period'
                        // ]
                    ],
                    [
                            [
                            'title' => 'Total Invoices',
                            'key' => 'total_invoices'
                            ],
                            [
                            'title' => 'Invoice Period',
                            'key' => 'invoice_preiod'
                            ],
                            [
                            'title' => 'Total Payable',
                            'key' => 'total_payable'
                            ]
                    ]
                ]
            ]
        ];
        //$this->meta['schema'] = $this->schema();
    }
}