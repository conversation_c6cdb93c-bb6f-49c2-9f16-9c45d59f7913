<?php

namespace App\Console\Commands\Workflows\Reports;

use App\Console\Commands\Workflow;

class IncidentalInvoiceDetailReportWorkflow extends Workflow
{
    protected $signature = 'workflow:IncidentalInvoiceDetailReport {input}';
    protected $description = 'Member Invoice Detail Report Workflow';
    protected $schema = [];

    public function getSchema()
    {
        $companyId = $this->request['company_id'];

        // Get charge types from income_common_area_charges
        $chargeTypes = $this->tenantDB()->table('income_common_area_charges')
            ->where('soc_id', $companyId)
            ->select('id', 'particular')
            ->get();

        // Generate dynamic columns based on charge types
        $dynamicColumns = [
            [
                "title" => "Unit Name",
                "key" => "unit_name"
            ],
            [
                "title" => "Bill To",
                "key" => "member_name"
            ],
            [
                "title" => "Invoice Number",
                "key" => "invoice_number"
            ]
        ];

        foreach ($chargeTypes as $type) {
            $columnKey = strtolower(preg_replace('/[^a-zA-Z0-9]/', '_', $type->particular));
            $columnKey = trim($columnKey, '_');

            if ($columnKey !== 'payment_reversal_correction') {
                $dynamicColumns[] = [
                    "title" => $type->particular,
                    "key" => $columnKey,
                    "type" => "number"
                ];
            }
        }

        // Fixed columns
        $fixedColumns = [
            [
                "title" => "Interest",
                "key" => "interest",
                "type" => "number"
            ],
            [
                "title" => "Tax",
                "key" => "tax",
                "type" => "number"
            ],
            [
                "title" => "Invoice Amount",
                "key" => "invoice_amount",
                "type" => "number"
            ],
            [
                "title" => "Credit/Adjustment",
                "key" => "credit_adjustment",
                "type" => "number"
            ],
            [
                "title" => "P Arrears",
                "key" => "p_arrears",
                "type" => "number"
            ],
            [
                "title" => "I Arrears",
                "key" => "i_arrears",
                "type" => "number"
            ],
            [
                "title" => "Net Due",
                "key" => "net_due",
                "type" => "number"
            ]
        ];

        $allColumns = array_merge($dynamicColumns, $fixedColumns);

        return [
            "table" => [
                "tableTitle" => [
                    "Member Incidental Invoice Detail",
                    "Summary"
                ],
                "extraFilters" => [
                    "invoice_date" => [
                        "type" => "date",
                        "label" => "Invoice Date",
                        "required" => true
                    ]
                ],
                "actions" => [
                    [
                        "title" => "Export Report",
                        "icon" => "ri-export-line",
                        "options" => [
                            [
                                "title" => "Print",
                                "icon" => "ri-file-2-line",
                                "api" => [
                                    "type" => "print",
                                    "url" => "/admin/income-details/incidentalInvoiceDetailReport/download/pdf",
                                    "method" => "GET",
                                ]
                            ],
                            [
                                "title" => "PDF",
                                "icon" => "ri-file-pdf-2-line",
                                "api" => [
                                    "type" => "download",
                                    "url" => "/admin/income-details/incidentalInvoiceDetailReport/download/pdf",
                                    "method" => "GET"
                                ]
                            ],
                            [
                                "title" => "Excel",
                                "icon" => "ri-file-excel-2-line",
                                "api" => [
                                    "type" => "download",
                                    "url" => "/admin/income-details/incidentalInvoiceDetailReport/download/excel",
                                    "method" => "GET"
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => ["*"],
                "columns" => array_merge(
                    $allColumns,
                    [
                        [
                            "title" => "Total Invoice",
                            "key" => "invoice_count"
                        ],
                        [
                            "title" => "Date",
                            "key" => "date",
                            "type" => "date"
                        ],
                        [
                            "title" => "Total Payable",
                            "key" => "total_payable",
                            "type" => "number"
                        ]
                    ]
                )
                ]
        ];

    }

    public function apply()
    {
        $this->schema = $this->getSchema();
        $report = $this->action('datasource:incidentalInvoiceDetailReport', $this->pointer, $this->request);
        $this->meta = [
            'schema' => $this->schema,
        ];
    }
}
