<?php

namespace App\Console\Commands\Workflows\Account\Transaction;

use App\Console\Commands\Workflow;

class ListTransactionDetailWorkflow extends Workflow
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workflow:listTransactionDetail {input?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Transaction Workflow';

    protected $schema = [];
    protected $formatterByKeys = [];
    /**
     * Execute the console command.
     */

    protected $rules = [
        "id" => "required|numeric|min:1",
        "ledger_id" => "required|numeric|min:1",
    ];

    protected $rulesMessage = [
        "id.required" => "Ledger Id is required",
        "id.numeric" => "Ledger Id must be numeric",
        "id.min" => "Ledger Id must be greater than 0",
        "ledger_id.required" => "Transaction Id is required",
        "ledger_id.numeric" => "Transaction Id must be numeric",
        "ledger_id.min" => "Transaction Id must be greater than 0",
    ];

    public function apply()
    {
        // month validation
        if(!empty($this->input['month'])){
            if ($this->input['month'] < 1 || $this->input['month'] > 12) {
                $this->message = "Month should be between 1 and 12";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }

        // year validation
        if(!empty($this->input['year'])){
            // check if year-04-01 is a valid date or not from soc_account_financial_year table
            $start_date = $this->input['year'] . '-04-01';
            $end_date = $this->input['year'] + 1 . '-03-31';
            $yearData = $this->tenantDB()->table('soc_account_financial_year_master')
                ->where('soc_id', $this->input['company_id'])
                ->where('fy_start_date', $start_date)
                ->where('fy_end_date', $end_date)
                ->first();

            if (!$yearData) {
                $this->message = "Invalid Year";
                $this->status = "error";
                $this->statusCode = 400;
                return;
            }
        }


        $data = $this->action('datasource:listTransactionDetail', $this->pointer, $this->request);
        $this->data = $data;
    }
}
